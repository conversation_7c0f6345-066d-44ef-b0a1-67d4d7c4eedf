<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Settings - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        /* Settings Form */
        .settings-container {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px var(--shadow-color);
            max-width: 800px;
        }
        
        .settings-section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .settings-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-help {
            font-size: 0.85rem;
            color: var(--text-color);
            opacity: 0.7;
            margin-top: 5px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-indicator.enabled {
            background-color: rgba(72, 199, 116, 0.2);
            color: #48c774;
        }
        
        .status-indicator.disabled {
            background-color: rgba(255, 107, 107, 0.2);
            color: var(--secondary-color);
        }
        
        .test-section {
            background-color: var(--bg-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .test-chat {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: var(--card-bg);
        }
        
        .test-message {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
        }
        
        .test-message.user {
            justify-content: flex-end;
        }
        
        .test-message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 15px;
            line-height: 1.4;
        }
        
        .test-message.bot .test-message-content {
            background-color: var(--bg-color);
        }
        
        .test-message.user .test-message-content {
            background-color: var(--primary-color);
            color: white;
        }
        
        .test-input-group {
            display: flex;
            gap: 10px;
        }
        
        .test-input-group input {
            flex: 1;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading i {
            font-size: 2rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }
            
            .settings-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="../index.html">Home</a></li>
                    <li><a href="../shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>
                
                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="admin-customers.html"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="admin-categories.html"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="admin-inventory.html"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="file-manager.html"><i class="fas fa-folder"></i> File Manager</a></li>
                    <li><a href="coupons.html"><i class="fas fa-ticket-alt"></i> Coupons</a></li>
                    <li><a href="chatbot-settings.html" class="active"><i class="fas fa-robot"></i> Chatbot</a></li>
                    <li><a href="admin-analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="admin-settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="../login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>
            
            <!-- Admin Content -->
            <div class="admin-content">
                <div class="admin-header">
                    <h1>Chatbot Settings</h1>
                    <div class="admin-actions">
                        <span class="status-indicator" id="statusIndicator">
                            <i class="fas fa-circle"></i>
                            <span id="statusText">Loading...</span>
                        </span>
                    </div>
                </div>
                
                <div class="settings-container">
                    <div class="loading" id="settingsLoading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading settings...</p>
                    </div>
                    
                    <form id="chatbotSettingsForm" style="display: none;">
                        <!-- Basic Settings -->
                        <div class="settings-section">
                            <h2 class="section-title">
                                <i class="fas fa-cog"></i>
                                Basic Settings
                            </h2>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="enabled" name="enabled">
                                    <label for="enabled">Enable Chatbot</label>
                                </div>
                                <div class="form-help">Turn the chatbot on or off for your website visitors</div>
                            </div>
                            
                            <div class="form-group">
                                <label for="welcomeMessage">Welcome Message</label>
                                <textarea id="welcomeMessage" name="welcome_message" placeholder="Enter the initial message shown to users"></textarea>
                                <div class="form-help">This message will be displayed when users first open the chatbot</div>
                            </div>
                        </div>
                        
                        <!-- API Configuration -->
                        <div class="settings-section">
                            <h2 class="section-title">
                                <i class="fas fa-key"></i>
                                OpenRouter API Configuration
                            </h2>
                            
                            <div class="form-group">
                                <label for="apiKey">OpenRouter API Key</label>
                                <input type="password" id="apiKey" name="openrouter_api_key" placeholder="Enter your OpenRouter API key">
                                <div class="form-help">Get your free API key from <a href="https://openrouter.ai" target="_blank">openrouter.ai</a></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="model">AI Model</label>
                                <select id="model" name="model">
                                    <option value="meta-llama/llama-3.2-3b-instruct:free">Llama 3.2 3B (Free)</option>
                                    <option value="meta-llama/llama-3.2-1b-instruct:free">Llama 3.2 1B (Free)</option>
                                    <option value="google/gemma-2-9b-it:free">Gemma 2 9B (Free)</option>
                                    <option value="microsoft/phi-3-mini-128k-instruct:free">Phi-3 Mini (Free)</option>
                                    <option value="huggingfaceh4/zephyr-7b-beta:free">Zephyr 7B (Free)</option>
                                </select>
                                <div class="form-help">Choose a free AI model for your chatbot</div>
                            </div>
                            
                            <div class="form-group">
                                <label for="apiUrl">API URL</label>
                                <input type="url" id="apiUrl" name="api_url" value="https://openrouter.ai/api/v1/chat/completions">
                                <div class="form-help">OpenRouter API endpoint (usually doesn't need to be changed)</div>
                            </div>
                        </div>
                        
                        <!-- Advanced Settings -->
                        <div class="settings-section">
                            <h2 class="section-title">
                                <i class="fas fa-sliders-h"></i>
                                Advanced Settings
                            </h2>
                            
                            <div class="form-group">
                                <label for="maxTokens">Max Tokens</label>
                                <input type="number" id="maxTokens" name="max_tokens" min="50" max="2000" value="500">
                                <div class="form-help">Maximum length of chatbot responses (50-2000)</div>
                            </div>
                            
                            <div class="form-group">
                                <label for="temperature">Temperature</label>
                                <input type="number" id="temperature" name="temperature" min="0" max="2" step="0.1" value="0.7">
                                <div class="form-help">Controls randomness: 0 = focused, 2 = creative (0.0-2.0)</div>
                            </div>
                        </div>
                        
                        <!-- Test Section -->
                        <div class="settings-section">
                            <h2 class="section-title">
                                <i class="fas fa-vial"></i>
                                Test Chatbot
                            </h2>
                            
                            <div class="test-section">
                                <div class="test-chat" id="testChat">
                                    <div class="test-message bot">
                                        <div class="test-message-content">
                                            Hello! I'm ready to help you test the chatbot. Send me a message!
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="test-input-group">
                                    <input type="text" id="testInput" placeholder="Type a test message..." maxlength="500">
                                    <button type="button" id="testSend" class="btn">Send</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn">Save Settings</button>
                            <button type="button" class="btn btn-secondary" id="resetBtn">Reset to Defaults</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="../js/chatbot-settings.js"></script>
</body>
</html>
