<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }
        
        /* Products Table */
        .products-table-container {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
            overflow-x: auto;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .products-table th,
        .products-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .products-table th {
            background-color: var(--bg-color);
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .products-table img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .product-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-align: center;
            display: inline-block;
        }
        
        .product-status.active {
            background-color: rgba(72, 199, 116, 0.2);
            color: #48c774;
        }
        
        .product-status.inactive {
            background-color: rgba(255, 107, 107, 0.2);
            color: var(--secondary-color);
        }
        
        .product-status.draft {
            background-color: rgba(255, 190, 11, 0.2);
            color: var(--accent-color);
        }
        
        .product-actions {
            display: flex;
            gap: 10px;
        }
        
        .product-actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
            padding: 5px;
        }
        
        .product-actions button:hover {
            opacity: 1;
            color: var(--primary-color);
        }
        
        .product-actions .delete-btn:hover {
            color: var(--secondary-color);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading i {
            font-size: 2rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: var(--card-bg);
            margin: 2% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px var(--shadow-color);
        }

        .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            color: var(--primary-color);
        }

        .close {
            color: var(--text-color);
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .close:hover {
            opacity: 1;
        }

        .modal-body {
            padding: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        /* Image Upload Section */
        .image-upload-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .image-upload-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .image-upload-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .image-upload-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .image-upload-content {
            display: none;
        }

        .image-upload-content.active {
            display: block;
        }

        .file-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: var(--primary-color);
            background-color: rgba(106, 61, 232, 0.05);
        }

        .file-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(106, 61, 232, 0.1);
        }

        .base64-upload-area {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .base64-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin: 10px auto;
            display: none;
        }

        .product-images-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .image-item {
            position: relative;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            background-color: var(--bg-color);
        }

        .image-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .image-item-controls {
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .image-item-controls button {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .image-item-controls button:hover {
            opacity: 1;
        }

        .primary-badge {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-secondary {
            background-color: var(--border-color);
            color: var(--text-color);
        }

        .btn-secondary:hover {
            background-color: var(--text-color);
            color: var(--bg-color);
        }

        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }

            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }

            .admin-sidebar.active {
                display: block;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 5% auto;
            }

            .product-images-gallery {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="../index.html">Home</a></li>
                    <li><a href="../shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>
                
                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html" class="active"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="admin-customers.html"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="admin-categories.html"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="admin-inventory.html"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="admin-analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="admin-settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="../login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>
            
            <!-- Admin Content -->
            <div class="admin-content">
                <div class="admin-header">
                    <h1>Products Management</h1>
                    <div class="admin-actions">
                        <button class="btn btn-small" id="addProductBtn"><i class="fas fa-plus"></i> Add Product</button>
                        <button class="btn btn-small"><i class="fas fa-download"></i> Export</button>
                    </div>
                </div>
                
                <!-- Products Table -->
                <div class="products-table-container">
                    <div class="loading" id="productsLoading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading products...</p>
                    </div>
                    
                    <table class="products-table" id="productsTable" style="display: none;">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Name</th>
                                <th>SKU</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- Products will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Product Modal -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Add Product</h2>
                <span class="close" id="closeModal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <input type="hidden" id="productId" name="id">

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="productName">Product Name *</label>
                            <input type="text" id="productName" name="name" required>
                        </div>

                        <div class="form-group">
                            <label for="productSku">SKU</label>
                            <input type="text" id="productSku" name="sku" placeholder="Auto-generated if empty">
                        </div>

                        <div class="form-group">
                            <label for="productPrice">Price *</label>
                            <input type="number" id="productPrice" name="price" step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="productSalePrice">Sale Price</label>
                            <input type="number" id="productSalePrice" name="sale_price" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="productStock">Stock Quantity</label>
                            <input type="number" id="productStock" name="stock_quantity" min="0" value="0">
                        </div>

                        <div class="form-group">
                            <label for="productCategory">Category</label>
                            <select id="productCategory" name="category_id">
                                <option value="">Select Category</option>
                                <!-- Categories will be loaded here -->
                            </select>
                        </div>

                        <div class="form-group full-width">
                            <label for="productShortDescription">Short Description</label>
                            <textarea id="productShortDescription" name="short_description" rows="3"></textarea>
                        </div>

                        <div class="form-group full-width">
                            <label for="productDescription">Description</label>
                            <textarea id="productDescription" name="description" rows="5"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="productWeight">Weight (kg)</label>
                            <input type="number" id="productWeight" name="weight" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="productDimensions">Dimensions</label>
                            <input type="text" id="productDimensions" name="dimensions" placeholder="L x W x H">
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="productFeatured" name="featured">
                                <label for="productFeatured">Featured Product</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="productStatus">Status</label>
                            <select id="productStatus" name="status">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="draft">Draft</option>
                            </select>
                        </div>
                    </div>

                    <!-- Image Upload Section -->
                    <div class="image-upload-section">
                        <h3>Product Images</h3>

                        <div class="image-upload-tabs">
                            <div class="image-upload-tab active" data-tab="file">File Upload</div>
                            <div class="image-upload-tab" data-tab="base64">Base64 Upload</div>
                        </div>

                        <!-- File Upload Tab -->
                        <div class="image-upload-content active" id="fileUploadTab">
                            <div class="file-upload-area" id="fileUploadArea">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>Drag & drop images here or click to browse</p>
                                <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                            </div>
                        </div>

                        <!-- Base64 Upload Tab -->
                        <div class="image-upload-content" id="base64UploadTab">
                            <div class="base64-upload-area">
                                <textarea id="base64Input" placeholder="Paste base64 image data here..." rows="4"></textarea>
                                <input type="text" id="base64AltText" placeholder="Alt text for image">
                                <button type="button" id="addBase64Image" class="btn btn-small">Add Image</button>
                                <img id="base64Preview" class="base64-preview" style="display: none;">
                            </div>
                        </div>

                        <!-- Product Images Gallery -->
                        <div class="product-images-gallery" id="productImagesGallery">
                            <!-- Images will be displayed here -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelBtn">Cancel</button>
                <button type="submit" form="productForm" class="btn" id="saveBtn">Save Product</button>
            </div>
        </div>
    </div>

    <script src="../js/admin-products.js"></script>
