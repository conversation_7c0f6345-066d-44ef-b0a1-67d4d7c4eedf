<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Auth.php';

try {
    $auth = new Auth();
    $auth->requireAdmin();
    
    $db = Database::getInstance();
    
    // Handle export request
    if (isset($_GET['export']) && $_GET['export'] === 'true') {
        exportAnalyticsReport($db);
        exit;
    }
    
    // Get filter parameters
    $days = isset($_GET['days']) ? (int)$_GET['days'] : 30;
    $startDate = $_GET['start_date'] ?? null;
    $endDate = $_GET['end_date'] ?? null;
    $categoryId = isset($_GET['category_id']) && $_GET['category_id'] !== '' ? (int)$_GET['category_id'] : null;
    
    // Calculate date range
    if ($startDate && $endDate) {
        $dateCondition = "DATE(created_at) BETWEEN ? AND ?";
        $dateParams = [$startDate, $endDate];
        $previousStartDate = date('Y-m-d', strtotime($startDate . ' -' . (strtotime($endDate) - strtotime($startDate)) / 86400 . ' days'));
        $previousEndDate = date('Y-m-d', strtotime($startDate . ' -1 day'));
    } else {
        $dateCondition = "created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        $dateParams = [$days];
        $previousStartDate = date('Y-m-d', strtotime("-" . ($days * 2) . " days"));
        $previousEndDate = date('Y-m-d', strtotime("-$days days"));
    }
    
    // Category condition
    $categoryCondition = $categoryId ? " AND p.category_id = ?" : "";
    $categoryParams = $categoryId ? [$categoryId] : [];
    
    // Get current period stats
    $currentStats = getCurrentPeriodStats($db, $dateCondition, $dateParams, $categoryCondition, $categoryParams);
    
    // Get previous period stats for comparison
    $previousStats = getPreviousPeriodStats($db, $previousStartDate, $previousEndDate, $categoryCondition, $categoryParams);
    
    // Calculate percentage changes
    $stats = calculateStatChanges($currentStats, $previousStats);
    
    // Get chart data
    $chartData = getChartData($db, $dateCondition, $dateParams, $categoryCondition, $categoryParams);
    
    echo json_encode([
        'success' => true,
        'data' => [
            'stats' => $stats,
            'charts' => $chartData
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function getCurrentPeriodStats($db, $dateCondition, $dateParams, $categoryCondition, $categoryParams) {
    // Revenue
    $revenueQuery = "SELECT COALESCE(SUM(total_amount), 0) as revenue 
                     FROM orders 
                     WHERE payment_status = 'paid' AND $dateCondition $categoryCondition";
    $revenueParams = array_merge($dateParams, $categoryParams);
    $revenue = $db->fetch($revenueQuery, $revenueParams)['revenue'];
    
    // Orders
    $ordersQuery = "SELECT COUNT(*) as orders 
                    FROM orders 
                    WHERE $dateCondition $categoryCondition";
    $orders = $db->fetch($ordersQuery, $revenueParams)['orders'];
    
    // New customers
    $customersQuery = "SELECT COUNT(*) as customers 
                       FROM users 
                       WHERE role = 'customer' AND $dateCondition";
    $customers = $db->fetch($customersQuery, $dateParams)['customers'];
    
    // Products sold
    $productsQuery = "SELECT COALESCE(SUM(oi.quantity), 0) as products_sold 
                      FROM order_items oi 
                      JOIN orders o ON oi.order_id = o.id 
                      WHERE o.payment_status = 'paid' AND $dateCondition $categoryCondition";
    $productsSold = $db->fetch($productsQuery, $revenueParams)['products_sold'];
    
    return [
        'revenue' => (float)$revenue,
        'orders' => (int)$orders,
        'customers' => (int)$customers,
        'products_sold' => (int)$productsSold
    ];
}

function getPreviousPeriodStats($db, $startDate, $endDate, $categoryCondition, $categoryParams) {
    $dateCondition = "DATE(created_at) BETWEEN ? AND ?";
    $dateParams = [$startDate, $endDate];
    
    return getCurrentPeriodStats($db, $dateCondition, $dateParams, $categoryCondition, $categoryParams);
}

function calculateStatChanges($current, $previous) {
    $stats = [];
    
    foreach ($current as $key => $value) {
        $previousValue = $previous[$key] ?? 0;
        $change = 0;
        
        if ($previousValue > 0) {
            $change = (($value - $previousValue) / $previousValue) * 100;
        } elseif ($value > 0) {
            $change = 100;
        }
        
        $stats[$key] = [
            'current' => $value,
            'previous' => $previousValue,
            'change' => round($change, 1)
        ];
    }
    
    return $stats;
}

function getChartData($db, $dateCondition, $dateParams, $categoryCondition, $categoryParams) {
    // Revenue over time
    $revenueOverTime = getRevenueOverTime($db, $dateCondition, $dateParams, $categoryCondition, $categoryParams);
    
    // Sales by category
    $salesByCategory = getSalesByCategory($db, $dateCondition, $dateParams);
    
    return [
        'revenue_over_time' => $revenueOverTime,
        'sales_by_category' => $salesByCategory
    ];
}

function getRevenueOverTime($db, $dateCondition, $dateParams, $categoryCondition, $categoryParams) {
    $query = "SELECT DATE(created_at) as date, COALESCE(SUM(total_amount), 0) as revenue 
              FROM orders 
              WHERE payment_status = 'paid' AND $dateCondition $categoryCondition
              GROUP BY DATE(created_at) 
              ORDER BY date ASC";
    
    $params = array_merge($dateParams, $categoryParams);
    $results = $db->fetchAll($query, $params);
    
    $labels = [];
    $values = [];
    
    foreach ($results as $row) {
        $labels[] = date('M j', strtotime($row['date']));
        $values[] = (float)$row['revenue'];
    }
    
    return [
        'labels' => $labels,
        'values' => $values
    ];
}

function getSalesByCategory($db, $dateCondition, $dateParams) {
    $query = "SELECT c.name, COALESCE(SUM(oi.quantity * oi.price), 0) as sales 
              FROM categories c 
              LEFT JOIN products p ON c.id = p.category_id 
              LEFT JOIN order_items oi ON p.id = oi.product_id 
              LEFT JOIN orders o ON oi.order_id = o.id 
              WHERE (o.id IS NULL OR (o.payment_status = 'paid' AND $dateCondition))
              GROUP BY c.id, c.name 
              HAVING sales > 0
              ORDER BY sales DESC 
              LIMIT 5";
    
    $results = $db->fetchAll($query, $dateParams);
    
    $labels = [];
    $values = [];
    
    foreach ($results as $row) {
        $labels[] = $row['name'];
        $values[] = (float)$row['sales'];
    }
    
    return [
        'labels' => $labels,
        'values' => $values
    ];
}

function exportAnalyticsReport($db) {
    // Set headers for CSV download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="analytics-report-' . date('Y-m-d') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // Get comprehensive analytics data
    $days = isset($_GET['days']) ? (int)$_GET['days'] : 30;
    $startDate = $_GET['start_date'] ?? null;
    $endDate = $_GET['end_date'] ?? null;
    $categoryId = isset($_GET['category_id']) && $_GET['category_id'] !== '' ? (int)$_GET['category_id'] : null;
    
    // Calculate date range
    if ($startDate && $endDate) {
        $dateCondition = "DATE(o.created_at) BETWEEN ? AND ?";
        $dateParams = [$startDate, $endDate];
    } else {
        $dateCondition = "o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        $dateParams = [$days];
    }
    
    // Category condition
    $categoryCondition = $categoryId ? " AND p.category_id = ?" : "";
    $categoryParams = $categoryId ? [$categoryId] : [];
    
    // Write CSV headers
    fputcsv($output, ['Analytics Report - Generated on ' . date('Y-m-d H:i:s')]);
    fputcsv($output, []);
    
    // Summary stats
    fputcsv($output, ['Summary Statistics']);
    fputcsv($output, ['Metric', 'Value']);
    
    $summaryQuery = "SELECT 
                        COUNT(DISTINCT o.id) as total_orders,
                        COALESCE(SUM(o.total_amount), 0) as total_revenue,
                        COUNT(DISTINCT o.user_id) as unique_customers,
                        COALESCE(SUM(oi.quantity), 0) as total_products_sold
                     FROM orders o 
                     LEFT JOIN order_items oi ON o.id = oi.order_id 
                     LEFT JOIN products p ON oi.product_id = p.id
                     WHERE o.payment_status = 'paid' AND $dateCondition $categoryCondition";
    
    $params = array_merge($dateParams, $categoryParams);
    $summary = $db->fetch($summaryQuery, $params);
    
    fputcsv($output, ['Total Orders', $summary['total_orders']]);
    fputcsv($output, ['Total Revenue', '$' . number_format($summary['total_revenue'], 2)]);
    fputcsv($output, ['Unique Customers', $summary['unique_customers']]);
    fputcsv($output, ['Total Products Sold', $summary['total_products_sold']]);
    
    fputcsv($output, []);
    
    // Top products
    fputcsv($output, ['Top Selling Products']);
    fputcsv($output, ['Product Name', 'Quantity Sold', 'Revenue']);
    
    $topProductsQuery = "SELECT p.name, SUM(oi.quantity) as qty, SUM(oi.total) as revenue 
                         FROM order_items oi 
                         JOIN products p ON oi.product_id = p.id 
                         JOIN orders o ON oi.order_id = o.id 
                         WHERE o.payment_status = 'paid' AND $dateCondition $categoryCondition
                         GROUP BY p.id, p.name 
                         ORDER BY qty DESC 
                         LIMIT 10";
    
    $topProducts = $db->fetchAll($topProductsQuery, $params);
    
    foreach ($topProducts as $product) {
        fputcsv($output, [
            $product['name'],
            $product['qty'],
            '$' . number_format($product['revenue'], 2)
        ]);
    }
    
    fclose($output);
}
?>
