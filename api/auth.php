<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Auth.php';
require_once '../includes/Cart.php';

try {
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'check':
                        // Check if user is logged in
                        if ($auth->isLoggedIn()) {
                            $user = $auth->getCurrentUser();
                            echo json_encode([
                                'success' => true,
                                'logged_in' => true,
                                'user' => [
                                    'id' => $user['id'],
                                    'username' => $user['username'],
                                    'email' => $user['email'],
                                    'first_name' => $user['first_name'],
                                    'last_name' => $user['last_name'],
                                    'role' => $user['role']
                                ]
                            ]);
                        } else {
                            echo json_encode([
                                'success' => true,
                                'logged_in' => false
                            ]);
                        }
                        break;
                        
                    case 'profile':
                        // Get user profile
                        $auth->requireLogin();
                        $user = $auth->getCurrentUser();
                        
                        echo json_encode([
                            'success' => true,
                            'data' => [
                                'id' => $user['id'],
                                'username' => $user['username'],
                                'email' => $user['email'],
                                'first_name' => $user['first_name'],
                                'last_name' => $user['last_name'],
                                'phone' => $user['phone'],
                                'role' => $user['role'],
                                'status' => $user['status'],
                                'email_verified' => $user['email_verified'],
                                'created_at' => $user['created_at']
                            ]
                        ]);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'login':
                        // User login
                        if (!isset($input['username']) || !isset($input['password'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Username and password required']);
                            break;
                        }
                        
                        $user = $auth->login($input['username'], $input['password']);
                        
                        if ($user) {
                            // Merge cart after login
                            $cart = new Cart();
                            $cart->mergeCarts($user['id']);
                            
                            echo json_encode([
                                'success' => true,
                                'message' => 'Login successful',
                                'user' => [
                                    'id' => $user['id'],
                                    'username' => $user['username'],
                                    'email' => $user['email'],
                                    'first_name' => $user['first_name'],
                                    'last_name' => $user['last_name'],
                                    'role' => $user['role']
                                ]
                            ]);
                        } else {
                            http_response_code(401);
                            echo json_encode(['success' => false, 'message' => 'Invalid credentials']);
                        }
                        break;
                        
                    case 'register':
                        // User registration
                        $requiredFields = ['username', 'email', 'password', 'first_name', 'last_name'];
                        
                        foreach ($requiredFields as $field) {
                            if (!isset($input[$field]) || empty(trim($input[$field]))) {
                                http_response_code(400);
                                echo json_encode(['success' => false, 'message' => ucfirst($field) . ' is required']);
                                break 2;
                            }
                        }
                        
                        // Validate email
                        if (!Utils::validateEmail($input['email'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Invalid email address']);
                            break;
                        }
                        
                        // Validate password strength
                        if (strlen($input['password']) < 6) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Password must be at least 6 characters long']);
                            break;
                        }
                        
                        $userData = [
                            'username' => Utils::sanitizeInput($input['username']),
                            'email' => Utils::sanitizeInput($input['email']),
                            'password' => $input['password'],
                            'first_name' => Utils::sanitizeInput($input['first_name']),
                            'last_name' => Utils::sanitizeInput($input['last_name']),
                            'phone' => isset($input['phone']) ? Utils::sanitizeInput($input['phone']) : null
                        ];
                        
                        $result = $auth->register($userData);
                        
                        if ($result['success']) {
                            http_response_code(201);
                            echo json_encode([
                                'success' => true,
                                'message' => 'Registration successful',
                                'user_id' => $result['user_id']
                            ]);
                        } else {
                            http_response_code(400);
                            echo json_encode($result);
                        }
                        break;
                        
                    case 'logout':
                        // User logout
                        $auth->logout();
                        echo json_encode(['success' => true, 'message' => 'Logout successful']);
                        break;
                        
                    case 'update_profile':
                        // Update user profile
                        $auth->requireLogin();
                        $user = $auth->getCurrentUser();
                        
                        $updateData = [];
                        $allowedFields = ['first_name', 'last_name', 'phone'];
                        
                        foreach ($allowedFields as $field) {
                            if (isset($input[$field])) {
                                $updateData[$field] = Utils::sanitizeInput($input[$field]);
                            }
                        }
                        
                        if (empty($updateData)) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'No valid fields to update']);
                            break;
                        }
                        
                        $db = Database::getInstance();
                        $result = $db->update('users', $updateData, 'id = ?', [$user['id']]);
                        
                        echo json_encode(['success' => true, 'message' => 'Profile updated successfully']);
                        break;
                        
                    case 'change_password':
                        // Change user password
                        $auth->requireLogin();
                        $user = $auth->getCurrentUser();
                        
                        if (!isset($input['current_password']) || !isset($input['new_password'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Current and new password required']);
                            break;
                        }
                        
                        // Verify current password
                        if (!password_verify($input['current_password'], $user['password'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Current password is incorrect']);
                            break;
                        }
                        
                        // Validate new password
                        if (strlen($input['new_password']) < 6) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'New password must be at least 6 characters long']);
                            break;
                        }
                        
                        $hashedPassword = password_hash($input['new_password'], PASSWORD_DEFAULT);
                        
                        $db = Database::getInstance();
                        $db->update('users', ['password' => $hashedPassword], 'id = ?', [$user['id']]);
                        
                        echo json_encode(['success' => true, 'message' => 'Password changed successfully']);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
