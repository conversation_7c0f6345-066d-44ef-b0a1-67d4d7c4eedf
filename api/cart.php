<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Cart.php';
require_once '../includes/Auth.php';

try {
    $cart = new Cart();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'count':
                        // Get cart item count
                        $count = $cart->getItemCount();
                        echo json_encode(['success' => true, 'count' => $count]);
                        break;
                        
                    case 'totals':
                        // Get cart totals
                        $totals = $cart->getTotals();
                        echo json_encode(['success' => true, 'data' => $totals]);
                        break;
                        
                    case 'validate':
                        // Validate cart
                        $validation = $cart->validateCart();
                        echo json_encode(['success' => true, 'data' => $validation]);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                // Get all cart items
                $items = $cart->getItems();
                $totals = $cart->getTotals();
                $appliedCoupon = $cart->getAppliedCoupon();
                
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'items' => $items,
                        'totals' => $totals,
                        'applied_coupon' => $appliedCoupon
                    ]
                ]);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'add':
                        // Add item to cart
                        if (!isset($input['product_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID required']);
                            break;
                        }
                        
                        $quantity = $input['quantity'] ?? 1;
                        $result = $cart->addItem($input['product_id'], $quantity);
                        
                        if ($result['success']) {
                            $count = $cart->getItemCount();
                            $result['cart_count'] = $count;
                        }
                        
                        echo json_encode($result);
                        break;
                        
                    case 'update':
                        // Update item quantity
                        if (!isset($input['product_id']) || !isset($input['quantity'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID and quantity required']);
                            break;
                        }
                        
                        $result = $cart->updateItem($input['product_id'], $input['quantity']);
                        echo json_encode($result);
                        break;
                        
                    case 'remove':
                        // Remove item from cart
                        if (!isset($input['product_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID required']);
                            break;
                        }
                        
                        $result = $cart->removeItem($input['product_id']);
                        echo json_encode($result);
                        break;
                        
                    case 'clear':
                        // Clear entire cart
                        $result = $cart->clearCart();
                        echo json_encode($result);
                        break;
                        
                    case 'apply_coupon':
                        // Apply coupon code
                        if (!isset($input['coupon_code'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Coupon code required']);
                            break;
                        }
                        
                        $result = $cart->applyCoupon($input['coupon_code']);
                        
                        if ($result['success']) {
                            $totals = $cart->getTotals();
                            $result['totals'] = $totals;
                        }
                        
                        echo json_encode($result);
                        break;
                        
                    case 'remove_coupon':
                        // Remove applied coupon
                        $result = $cart->removeCoupon();
                        
                        if ($result['success']) {
                            $totals = $cart->getTotals();
                            $result['totals'] = $totals;
                        }
                        
                        echo json_encode($result);
                        break;
                        
                    case 'merge':
                        // Merge session cart with user cart (after login)
                        if (!$auth->isLoggedIn()) {
                            http_response_code(401);
                            echo json_encode(['success' => false, 'message' => 'User not logged in']);
                            break;
                        }
                        
                        $user = $auth->getCurrentUser();
                        $result = $cart->mergeCarts($user['id']);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
