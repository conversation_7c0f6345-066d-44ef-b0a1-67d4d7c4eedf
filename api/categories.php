<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Auth.php';

try {
    $db = Database::getInstance();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                // Get single category by ID
                $category = $db->fetch(
                    "SELECT * FROM categories WHERE id = ?",
                    [$_GET['id']]
                );
                
                if ($category) {
                    echo json_encode(['success' => true, 'data' => $category]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Category not found']);
                }
            } else {
                // Get all categories
                $filters = [
                    'status' => $_GET['status'] ?? 'active',
                    'parent_id' => $_GET['parent_id'] ?? null
                ];
                
                $sql = "SELECT * FROM categories WHERE status = ?";
                $params = [$filters['status']];
                
                if ($filters['parent_id'] !== null) {
                    if ($filters['parent_id'] === '0' || $filters['parent_id'] === 'null') {
                        $sql .= " AND parent_id IS NULL";
                    } else {
                        $sql .= " AND parent_id = ?";
                        $params[] = $filters['parent_id'];
                    }
                }
                
                $sql .= " ORDER BY sort_order ASC, name ASC";
                
                $categories = $db->fetchAll($sql, $params);
                echo json_encode(['success' => true, 'data' => $categories]);
            }
            break;
            
        case 'POST':
            // Create new category (admin only)
            $auth->requireAdmin();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            // Validate required fields
            if (empty($input['name'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Category name is required']);
                break;
            }
            
            // Generate slug if not provided
            if (empty($input['slug'])) {
                $input['slug'] = Utils::generateSlug($input['name']);
            }
            
            // Check if slug already exists
            $existingCategory = $db->fetch(
                "SELECT id FROM categories WHERE slug = ?",
                [$input['slug']]
            );
            
            if ($existingCategory) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Category slug already exists']);
                break;
            }
            
            // Prepare category data
            $categoryData = [
                'name' => Utils::sanitizeInput($input['name']),
                'slug' => $input['slug'],
                'description' => isset($input['description']) ? Utils::sanitizeInput($input['description']) : null,
                'image' => isset($input['image']) ? $input['image'] : null,
                'parent_id' => isset($input['parent_id']) && $input['parent_id'] !== '' ? (int)$input['parent_id'] : null,
                'sort_order' => isset($input['sort_order']) ? (int)$input['sort_order'] : 0,
                'status' => isset($input['status']) ? $input['status'] : 'active'
            ];
            
            try {
                $categoryId = $db->insert('categories', $categoryData);
                
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'message' => 'Category created successfully',
                    'category_id' => $categoryId
                ]);
            } catch (Exception $e) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Failed to create category: ' . $e->getMessage()]);
            }
            break;
            
        case 'PUT':
            // Update category (admin only)
            $auth->requireAdmin();
            
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Category ID required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            // Check if category exists
            $category = $db->fetch("SELECT * FROM categories WHERE id = ?", [$_GET['id']]);
            
            if (!$category) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Category not found']);
                break;
            }
            
            // Prepare update data
            $updateData = [];
            $allowedFields = ['name', 'slug', 'description', 'image', 'parent_id', 'sort_order', 'status'];
            
            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    if ($field === 'name' || $field === 'description') {
                        $updateData[$field] = Utils::sanitizeInput($input[$field]);
                    } elseif ($field === 'parent_id') {
                        $updateData[$field] = $input[$field] !== '' ? (int)$input[$field] : null;
                    } elseif ($field === 'sort_order') {
                        $updateData[$field] = (int)$input[$field];
                    } else {
                        $updateData[$field] = $input[$field];
                    }
                }
            }
            
            // Generate slug if name changed but slug not provided
            if (isset($updateData['name']) && !isset($updateData['slug'])) {
                $updateData['slug'] = Utils::generateSlug($updateData['name']);
            }
            
            // Check if slug already exists (excluding current category)
            if (isset($updateData['slug'])) {
                $existingCategory = $db->fetch(
                    "SELECT id FROM categories WHERE slug = ? AND id != ?",
                    [$updateData['slug'], $_GET['id']]
                );
                
                if ($existingCategory) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Category slug already exists']);
                    break;
                }
            }
            
            if (empty($updateData)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'No valid fields to update']);
                break;
            }
            
            try {
                $db->update('categories', $updateData, 'id = ?', [$_GET['id']]);
                echo json_encode(['success' => true, 'message' => 'Category updated successfully']);
            } catch (Exception $e) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Failed to update category: ' . $e->getMessage()]);
            }
            break;
            
        case 'DELETE':
            // Delete category (admin only)
            $auth->requireAdmin();
            
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Category ID required']);
                break;
            }
            
            // Check if category exists
            $category = $db->fetch("SELECT * FROM categories WHERE id = ?", [$_GET['id']]);
            
            if (!$category) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Category not found']);
                break;
            }
            
            // Check if category has products
            $productCount = $db->fetch(
                "SELECT COUNT(*) as count FROM products WHERE category_id = ?",
                [$_GET['id']]
            );
            
            if ($productCount['count'] > 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false, 
                    'message' => 'Cannot delete category with existing products. Please move or delete products first.'
                ]);
                break;
            }
            
            // Check if category has subcategories
            $subcategoryCount = $db->fetch(
                "SELECT COUNT(*) as count FROM categories WHERE parent_id = ?",
                [$_GET['id']]
            );
            
            if ($subcategoryCount['count'] > 0) {
                http_response_code(400);
                echo json_encode([
                    'success' => false, 
                    'message' => 'Cannot delete category with subcategories. Please move or delete subcategories first.'
                ]);
                break;
            }
            
            try {
                $db->delete('categories', 'id = ?', [$_GET['id']]);
                echo json_encode(['success' => true, 'message' => 'Category deleted successfully']);
            } catch (Exception $e) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Failed to delete category: ' . $e->getMessage()]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
