<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Chatbot.php';
require_once '../includes/Auth.php';

try {
    $chatbot = new Chatbot();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'status':
                        // Check if chatbot is enabled
                        echo json_encode([
                            'success' => true,
                            'enabled' => $chatbot->isEnabled(),
                            'welcome_message' => $chatbot->getSettings()['welcome_message'] ?? 'Hello! How can I help you today?'
                        ]);
                        break;
                        
                    case 'conversations':
                        // Get user conversations
                        $userId = null;
                        if ($auth->isLoggedIn()) {
                            $user = $auth->getCurrentUser();
                            $userId = $user['id'];
                        }
                        
                        if (!$userId) {
                            echo json_encode(['success' => false, 'message' => 'Login required']);
                            break;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                        $conversations = $chatbot->getConversations($userId, $limit);
                        
                        echo json_encode(['success' => true, 'data' => $conversations]);
                        break;
                        
                    case 'messages':
                        // Get conversation messages
                        if (!isset($_GET['conversation_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Conversation ID required']);
                            break;
                        }
                        
                        $userId = null;
                        if ($auth->isLoggedIn()) {
                            $user = $auth->getCurrentUser();
                            $userId = $user['id'];
                        }
                        
                        $messages = $chatbot->getConversationMessages($_GET['conversation_id'], $userId);
                        echo json_encode(['success' => true, 'data' => $messages]);
                        break;
                        
                    case 'settings':
                        // Get chatbot settings (admin only)
                        $auth->requireAdmin();
                        
                        $settings = $chatbot->getSettings();
                        // Don't expose API key in response
                        if (isset($settings['openrouter_api_key'])) {
                            $settings['api_key_set'] = !empty($settings['openrouter_api_key']);
                            unset($settings['openrouter_api_key']);
                        }
                        
                        echo json_encode(['success' => true, 'data' => $settings]);
                        break;
                        
                    case 'stats':
                        // Get chatbot statistics (admin only)
                        $auth->requireAdmin();
                        
                        $dateFrom = $_GET['date_from'] ?? null;
                        $dateTo = $_GET['date_to'] ?? null;
                        
                        $stats = $chatbot->getStats($dateFrom, $dateTo);
                        echo json_encode(['success' => true, 'data' => $stats]);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'send_message':
                        // Send message to chatbot
                        if (!isset($input['message']) || empty(trim($input['message']))) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Message is required']);
                            break;
                        }
                        
                        if (!$chatbot->isEnabled()) {
                            echo json_encode([
                                'success' => false, 
                                'message' => 'Chatbot is currently unavailable. Please try again later.'
                            ]);
                            break;
                        }
                        
                        $userId = null;
                        if ($auth->isLoggedIn()) {
                            $user = $auth->getCurrentUser();
                            $userId = $user['id'];
                        }
                        
                        $conversationId = $input['conversation_id'] ?? null;
                        $message = trim($input['message']);
                        
                        // Rate limiting for anonymous users
                        if (!$userId) {
                            $sessionKey = 'chatbot_last_message_' . session_id();
                            $lastMessage = $_SESSION[$sessionKey] ?? 0;
                            
                            if (time() - $lastMessage < 5) { // 5 second cooldown
                                echo json_encode([
                                    'success' => false,
                                    'message' => 'Please wait a moment before sending another message.'
                                ]);
                                break;
                            }
                            
                            $_SESSION[$sessionKey] = time();
                        }
                        
                        $response = $chatbot->sendMessage($message, $userId, $conversationId);
                        echo json_encode($response);
                        break;
                        
                    case 'update_settings':
                        // Update chatbot settings (admin only)
                        $auth->requireAdmin();
                        
                        $result = $chatbot->updateSettings($input);
                        
                        if ($result['success']) {
                            echo json_encode($result);
                        } else {
                            http_response_code(400);
                            echo json_encode($result);
                        }
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        case 'DELETE':
            // Delete conversation
            if (!isset($_GET['conversation_id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Conversation ID required']);
                break;
            }
            
            $userId = null;
            if ($auth->isLoggedIn()) {
                $user = $auth->getCurrentUser();
                $userId = $user['id'];
            }
            
            if (!$userId) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Login required']);
                break;
            }
            
            $result = $chatbot->deleteConversation($_GET['conversation_id'], $userId);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
