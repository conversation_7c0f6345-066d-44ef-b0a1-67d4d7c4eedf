<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Coupon.php';
require_once '../includes/Auth.php';

try {
    $coupon = new Coupon();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'validate':
                        // Validate coupon code
                        if (!isset($_GET['code'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Coupon code required']);
                            break;
                        }
                        
                        $cartTotal = isset($_GET['cart_total']) ? (float)$_GET['cart_total'] : 0;
                        $userId = null;
                        
                        if ($auth->isLoggedIn()) {
                            $user = $auth->getCurrentUser();
                            $userId = $user['id'];
                        }
                        
                        $validation = $coupon->validateCoupon($_GET['code'], $cartTotal, $userId);
                        
                        if ($validation['valid']) {
                            echo json_encode([
                                'success' => true,
                                'valid' => true,
                                'discount' => $validation['discount'],
                                'message' => $validation['message'],
                                'coupon' => [
                                    'id' => $validation['coupon']['id'],
                                    'code' => $validation['coupon']['code'],
                                    'type' => $validation['coupon']['type'],
                                    'value' => $validation['coupon']['value']
                                ]
                            ]);
                        } else {
                            echo json_encode([
                                'success' => true,
                                'valid' => false,
                                'message' => $validation['message']
                            ]);
                        }
                        break;
                        
                    case 'stats':
                        // Get coupon statistics (admin only)
                        $auth->requireAdmin();
                        
                        $dateFrom = $_GET['date_from'] ?? null;
                        $dateTo = $_GET['date_to'] ?? null;
                        
                        $stats = $coupon->getCouponStats($dateFrom, $dateTo);
                        echo json_encode(['success' => true, 'data' => $stats]);
                        break;
                        
                    case 'usage':
                        // Get coupon usage history (admin only)
                        $auth->requireAdmin();
                        
                        if (!isset($_GET['coupon_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Coupon ID required']);
                            break;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $usage = $coupon->getCouponUsage($_GET['coupon_id'], $limit, $offset);
                        echo json_encode(['success' => true, 'data' => $usage]);
                        break;
                        
                    case 'generate_code':
                        // Generate coupon code (admin only)
                        $auth->requireAdmin();
                        
                        $prefix = $_GET['prefix'] ?? '';
                        $length = isset($_GET['length']) ? (int)$_GET['length'] : 8;
                        
                        $generatedCode = $coupon->generateCouponCode($prefix, $length);
                        echo json_encode(['success' => true, 'code' => $generatedCode]);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } elseif (isset($_GET['id'])) {
                // Get single coupon by ID (admin only)
                $auth->requireAdmin();
                
                $couponData = $coupon->getCouponById($_GET['id']);
                
                if ($couponData) {
                    echo json_encode(['success' => true, 'data' => $couponData]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Coupon not found']);
                }
            } elseif (isset($_GET['code'])) {
                // Get single coupon by code (admin only)
                $auth->requireAdmin();
                
                $couponData = $coupon->getCouponByCode($_GET['code']);
                
                if ($couponData) {
                    echo json_encode(['success' => true, 'data' => $couponData]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Coupon not found']);
                }
            } else {
                // Get all coupons (admin only)
                $auth->requireAdmin();
                
                $filters = [
                    'status' => $_GET['status'] ?? null,
                    'type' => $_GET['type'] ?? null,
                    'search' => $_GET['search'] ?? null,
                    'active_only' => isset($_GET['active_only']) ? true : null,
                    'sort' => $_GET['sort'] ?? 'newest',
                    'limit' => isset($_GET['limit']) ? (int)$_GET['limit'] : null,
                    'offset' => isset($_GET['offset']) ? (int)$_GET['offset'] : 0
                ];
                
                $coupons = $coupon->getAllCoupons($filters);
                echo json_encode(['success' => true, 'data' => $coupons]);
            }
            break;
            
        case 'POST':
            // Create new coupon (admin only)
            $auth->requireAdmin();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            $result = $coupon->createCoupon($input);
            
            if ($result['success']) {
                http_response_code(201);
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        case 'PUT':
            // Update coupon (admin only)
            $auth->requireAdmin();
            
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Coupon ID required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            $result = $coupon->updateCoupon($_GET['id'], $input);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        case 'DELETE':
            // Delete coupon (admin only)
            $auth->requireAdmin();
            
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Coupon ID required']);
                break;
            }
            
            $result = $coupon->deleteCoupon($_GET['id']);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
