<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/FileManager.php';
require_once '../includes/Auth.php';

try {
    $fileManager = new FileManager();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'stats':
                        // Get storage statistics (admin only)
                        $auth->requireAdmin();
                        $stats = $fileManager->getStorageStats();
                        echo json_encode(['success' => true, 'data' => $stats]);
                        break;
                        
                    case 'download':
                        // Download file
                        if (!isset($_GET['id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'File ID required']);
                            break;
                        }
                        
                        $file = $fileManager->getFileById($_GET['id']);
                        
                        if (!$file) {
                            http_response_code(404);
                            echo json_encode(['success' => false, 'message' => 'File not found']);
                            break;
                        }
                        
                        // Check if file is public or user has access
                        if (!$file['is_public']) {
                            $auth->requireLogin();
                            $user = $auth->getCurrentUser();
                            
                            // Allow access if user is admin or file owner
                            if (!$auth->isAdmin() && $file['uploaded_by'] != $user['id']) {
                                http_response_code(403);
                                echo json_encode(['success' => false, 'message' => 'Access denied']);
                                break;
                            }
                        }
                        
                        // Serve file
                        if (file_exists($file['file_path'])) {
                            header('Content-Type: ' . $file['mime_type']);
                            header('Content-Disposition: attachment; filename="' . $file['original_filename'] . '"');
                            header('Content-Length: ' . $file['file_size']);
                            readfile($file['file_path']);
                            exit;
                        } else {
                            http_response_code(404);
                            echo json_encode(['success' => false, 'message' => 'File not found on disk']);
                        }
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } elseif (isset($_GET['id'])) {
                // Get single file by ID
                $file = $fileManager->getFileById($_GET['id']);
                
                if ($file) {
                    // Check access permissions
                    if (!$file['is_public']) {
                        $auth->requireLogin();
                        $user = $auth->getCurrentUser();
                        
                        if (!$auth->isAdmin() && $file['uploaded_by'] != $user['id']) {
                            http_response_code(403);
                            echo json_encode(['success' => false, 'message' => 'Access denied']);
                            break;
                        }
                    }
                    
                    echo json_encode(['success' => true, 'data' => $file]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'File not found']);
                }
            } else {
                // Get all files with filters
                $filters = [
                    'file_type' => $_GET['file_type'] ?? null,
                    'search' => $_GET['search'] ?? null,
                    'sort' => $_GET['sort'] ?? 'newest',
                    'limit' => isset($_GET['limit']) ? (int)$_GET['limit'] : null,
                    'offset' => isset($_GET['offset']) ? (int)$_GET['offset'] : 0
                ];
                
                // If not admin, only show public files or user's own files
                if (!$auth->isAdmin()) {
                    if ($auth->isLoggedIn()) {
                        $user = $auth->getCurrentUser();
                        // This would require modifying the FileManager to support this filter
                        $filters['public_or_owned_by'] = $user['id'];
                    } else {
                        $filters['is_public'] = true;
                    }
                }
                
                $files = $fileManager->getAllFiles($filters);
                echo json_encode(['success' => true, 'data' => $files]);
            }
            break;
            
        case 'POST':
            if (isset($_POST['action'])) {
                switch ($_POST['action']) {
                    case 'upload':
                        // Upload single file
                        if (!isset($_FILES['file'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'No file uploaded']);
                            break;
                        }
                        
                        $uploadedBy = null;
                        if ($auth->isLoggedIn()) {
                            $user = $auth->getCurrentUser();
                            $uploadedBy = $user['id'];
                        }
                        
                        $description = $_POST['description'] ?? '';
                        
                        $result = $fileManager->uploadFile($_FILES['file'], $uploadedBy, $description);
                        
                        if ($result['success']) {
                            http_response_code(201);
                        } else {
                            http_response_code(400);
                        }
                        
                        echo json_encode($result);
                        break;
                        
                    case 'upload_multiple':
                        // Upload multiple files
                        if (!isset($_FILES['files'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'No files uploaded']);
                            break;
                        }
                        
                        $uploadedBy = null;
                        if ($auth->isLoggedIn()) {
                            $user = $auth->getCurrentUser();
                            $uploadedBy = $user['id'];
                        }
                        
                        $result = $fileManager->uploadMultipleFiles($_FILES['files'], $uploadedBy);
                        
                        if ($result['success']) {
                            http_response_code(201);
                        } else {
                            http_response_code(400);
                        }
                        
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        case 'PUT':
            // Update file details
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'File ID required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            // Check if file exists and user has permission
            $file = $fileManager->getFileById($_GET['id']);
            
            if (!$file) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'File not found']);
                break;
            }
            
            // Check permissions
            if (!$auth->isAdmin()) {
                $auth->requireLogin();
                $user = $auth->getCurrentUser();
                
                if ($file['uploaded_by'] != $user['id']) {
                    http_response_code(403);
                    echo json_encode(['success' => false, 'message' => 'Access denied']);
                    break;
                }
            }
            
            $result = $fileManager->updateFile($_GET['id'], $input);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        case 'DELETE':
            // Delete file
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'File ID required']);
                break;
            }
            
            // Check if file exists and user has permission
            $file = $fileManager->getFileById($_GET['id']);
            
            if (!$file) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'File not found']);
                break;
            }
            
            // Check permissions
            if (!$auth->isAdmin()) {
                $auth->requireLogin();
                $user = $auth->getCurrentUser();
                
                if ($file['uploaded_by'] != $user['id']) {
                    http_response_code(403);
                    echo json_encode(['success' => false, 'message' => 'Access denied']);
                    break;
                }
            }
            
            $result = $fileManager->deleteFile($_GET['id']);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
