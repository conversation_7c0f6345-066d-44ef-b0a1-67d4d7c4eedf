<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Newsletter.php';
require_once '../includes/Auth.php';

try {
    $newsletter = new Newsletter();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    // Get current user
    $user = null;
    if ($auth->isLoggedIn()) {
        $user = $auth->getCurrentUser();
    }
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'subscribers':
                        $auth->requireAdmin();
                        
                        $status = $_GET['status'] ?? 'active';
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 1000;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $result = $newsletter->getSubscribers($status, $limit, $offset);
                        echo json_encode($result);
                        break;
                        
                    case 'stats':
                        $auth->requireAdmin();
                        
                        $result = $newsletter->getSubscriberStats();
                        echo json_encode($result);
                        break;
                        
                    case 'confirm':
                        if (!isset($_GET['token'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Confirmation token required']);
                            break;
                        }
                        
                        $result = $newsletter->confirmSubscription($_GET['token']);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'subscribe':
                        if (!isset($input['email'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Email required']);
                            break;
                        }
                        
                        $firstName = $input['first_name'] ?? '';
                        $lastName = $input['last_name'] ?? '';
                        $preferences = $input['preferences'] ?? [];
                        
                        $result = $newsletter->subscribe($input['email'], $firstName, $lastName, $preferences);
                        echo json_encode($result);
                        break;
                        
                    case 'unsubscribe':
                        if (!isset($input['email'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Email required']);
                            break;
                        }
                        
                        $token = $input['token'] ?? null;
                        $result = $newsletter->unsubscribe($input['email'], $token);
                        echo json_encode($result);
                        break;
                        
                    case 'update_preferences':
                        if (!isset($input['email']) || !isset($input['preferences'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Email and preferences required']);
                            break;
                        }
                        
                        $result = $newsletter->updatePreferences($input['email'], $input['preferences']);
                        echo json_encode($result);
                        break;
                        
                    case 'create_campaign':
                        $auth->requireAdmin();
                        
                        if (!isset($input['subject']) || !isset($input['content'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Subject and content required']);
                            break;
                        }
                        
                        $targetAudience = $input['target_audience'] ?? 'active';
                        $result = $newsletter->createCampaign($input['subject'], $input['content'], $targetAudience);
                        echo json_encode($result);
                        break;
                        
                    case 'send_campaign':
                        $auth->requireAdmin();
                        
                        if (!isset($input['campaign_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Campaign ID required']);
                            break;
                        }
                        
                        $result = $newsletter->sendCampaign($input['campaign_id']);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                // Default: subscribe
                if (!isset($input['email'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Email required']);
                    break;
                }
                
                $firstName = $input['first_name'] ?? '';
                $lastName = $input['last_name'] ?? '';
                $preferences = $input['preferences'] ?? [];
                
                $result = $newsletter->subscribe($input['email'], $firstName, $lastName, $preferences);
                echo json_encode($result);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
