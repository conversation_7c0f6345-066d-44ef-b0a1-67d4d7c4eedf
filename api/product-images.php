<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Product.php';
require_once '../includes/Auth.php';

try {
    $product = new Product();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['product_id'])) {
                // Get all images for a product
                $images = $product->getProductImages($_GET['product_id']);
                echo json_encode(['success' => true, 'data' => $images]);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Product ID required']);
            }
            break;
            
        case 'POST':
            // Add new product image (admin only)
            $auth->requireAdmin();
            
            if (isset($_POST['action'])) {
                switch ($_POST['action']) {
                    case 'upload_file':
                        // Handle file upload
                        if (!isset($_POST['product_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID required']);
                            break;
                        }
                        
                        if (!isset($_FILES['image'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'No image file uploaded']);
                            break;
                        }
                        
                        $uploadResult = Utils::uploadFile($_FILES['image'], 'uploads/products/', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                        
                        if (!$uploadResult['success']) {
                            http_response_code(400);
                            echo json_encode($uploadResult);
                            break;
                        }
                        
                        // Create thumbnail
                        $thumbnailPath = 'uploads/products/thumb_' . $uploadResult['filename'];
                        Utils::resizeImage($uploadResult['filepath'], $thumbnailPath, 300, 300);
                        
                        $imageData = [
                            'image_path' => $uploadResult['filepath'],
                            'image_type' => 'file',
                            'alt_text' => $_POST['alt_text'] ?? '',
                            'sort_order' => isset($_POST['sort_order']) ? (int)$_POST['sort_order'] : 0
                        ];
                        
                        $result = $product->addProductImage($_POST['product_id'], $imageData);
                        
                        if ($result['success']) {
                            $result['image_data'] = [
                                'id' => $result['image_id'],
                                'image_path' => $uploadResult['filepath'],
                                'thumbnail_path' => $thumbnailPath,
                                'original_name' => $uploadResult['original_name'],
                                'size' => $uploadResult['size']
                            ];
                        }
                        
                        echo json_encode($result);
                        break;
                        
                    case 'upload_base64':
                        // Handle base64 image upload
                        $input = json_decode(file_get_contents('php://input'), true);
                        
                        if (!isset($input['product_id']) || !isset($input['image_data'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID and image data required']);
                            break;
                        }
                        
                        // Validate base64 image
                        if (!preg_match('/^data:image\/(\w+);base64,/', $input['image_data'], $matches)) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Invalid base64 image format']);
                            break;
                        }
                        
                        $imageType = $matches[1];
                        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                        
                        if (!in_array(strtolower($imageType), $allowedTypes)) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Image type not allowed']);
                            break;
                        }
                        
                        // Extract base64 data
                        $base64Data = substr($input['image_data'], strpos($input['image_data'], ',') + 1);
                        $imageData = base64_decode($base64Data);
                        
                        if ($imageData === false) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Invalid base64 data']);
                            break;
                        }
                        
                        // Save as file for thumbnail generation
                        $filename = uniqid() . '.' . $imageType;
                        $filepath = 'uploads/products/' . $filename;
                        
                        if (!is_dir('uploads/products/')) {
                            mkdir('uploads/products/', 0755, true);
                        }
                        
                        file_put_contents($filepath, $imageData);
                        
                        // Create thumbnail
                        $thumbnailPath = 'uploads/products/thumb_' . $filename;
                        Utils::resizeImage($filepath, $thumbnailPath, 300, 300);
                        
                        $productImageData = [
                            'image_path' => $filepath,
                            'image_data' => $input['image_data'],
                            'image_type' => 'base64',
                            'alt_text' => $input['alt_text'] ?? '',
                            'sort_order' => isset($input['sort_order']) ? (int)$input['sort_order'] : 0
                        ];
                        
                        $result = $product->addProductImage($input['product_id'], $productImageData);
                        
                        if ($result['success']) {
                            $result['image_data'] = [
                                'id' => $result['image_id'],
                                'image_path' => $filepath,
                                'thumbnail_path' => $thumbnailPath,
                                'size' => strlen($imageData)
                            ];
                        }
                        
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        case 'PUT':
            // Update product image (admin only)
            $auth->requireAdmin();
            
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Image ID required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'set_primary':
                        // Set image as primary
                        $result = $product->setPrimaryImage($_GET['id']);
                        echo json_encode($result);
                        break;
                        
                    case 'update_details':
                        // Update image details
                        $updateData = [];
                        $allowedFields = ['alt_text', 'sort_order'];
                        
                        foreach ($allowedFields as $field) {
                            if (isset($input[$field])) {
                                $updateData[$field] = $input[$field];
                            }
                        }
                        
                        if (empty($updateData)) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'No valid fields to update']);
                            break;
                        }
                        
                        $result = $product->updateProductImage($_GET['id'], $updateData);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        case 'DELETE':
            // Delete product image (admin only)
            $auth->requireAdmin();
            
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Image ID required']);
                break;
            }
            
            $result = $product->deleteProductImage($_GET['id']);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
