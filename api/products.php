<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Product.php';
require_once '../includes/Auth.php';

try {
    $product = new Product();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                // Get single product by ID
                $productData = $product->getProductById($_GET['id']);
                if ($productData) {
                    echo json_encode(['success' => true, 'data' => $productData]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Product not found']);
                }
            } elseif (isset($_GET['slug'])) {
                // Get single product by slug
                $productData = $product->getProductBySlug($_GET['slug']);
                if ($productData) {
                    echo json_encode(['success' => true, 'data' => $productData]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Product not found']);
                }
            } elseif (isset($_GET['featured'])) {
                // Get featured products
                $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 8;
                $products = $product->getFeaturedProducts($limit);
                echo json_encode(['success' => true, 'data' => $products]);
            } elseif (isset($_GET['related']) && isset($_GET['product_id'])) {
                // Get related products
                $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 4;
                $products = $product->getRelatedProducts($_GET['product_id'], $limit);
                echo json_encode(['success' => true, 'data' => $products]);
            } elseif (isset($_GET['search'])) {
                // Search products
                $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                $products = $product->searchProducts($_GET['search'], $limit);
                echo json_encode(['success' => true, 'data' => $products]);
            } else {
                // Get all products with filters
                $filters = [
                    'category_id' => $_GET['category_id'] ?? null,
                    'search' => $_GET['q'] ?? null,
                    'featured' => isset($_GET['featured']) ? true : null,
                    'min_price' => $_GET['min_price'] ?? null,
                    'max_price' => $_GET['max_price'] ?? null,
                    'sort' => $_GET['sort'] ?? 'name',
                    'limit' => isset($_GET['limit']) ? (int)$_GET['limit'] : null,
                    'offset' => isset($_GET['offset']) ? (int)$_GET['offset'] : 0
                ];
                
                $products = $product->getAllProducts($filters);
                echo json_encode(['success' => true, 'data' => $products]);
            }
            break;
            
        case 'POST':
            // Create new product (admin only)
            $auth->requireAdmin();
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            $result = $product->createProduct($input);
            
            if ($result['success']) {
                http_response_code(201);
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        case 'PUT':
            // Update product (admin only)
            $auth->requireAdmin();
            
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Product ID required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            $result = $product->updateProduct($_GET['id'], $input);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        case 'DELETE':
            // Delete product (admin only)
            $auth->requireAdmin();
            
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Product ID required']);
                break;
            }
            
            $result = $product->deleteProduct($_GET['id']);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
