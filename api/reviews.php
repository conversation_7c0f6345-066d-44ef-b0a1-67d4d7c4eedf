<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Reviews.php';
require_once '../includes/Auth.php';

try {
    $reviews = new Reviews();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    // Get current user
    $user = null;
    if ($auth->isLoggedIn()) {
        $user = $auth->getCurrentUser();
    }
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'product':
                        if (!isset($_GET['product_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID required']);
                            break;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $result = $reviews->getProductReviews($_GET['product_id'], $limit, $offset);
                        echo json_encode($result);
                        break;
                        
                    case 'user':
                        if (!$user) {
                            http_response_code(401);
                            echo json_encode(['success' => false, 'message' => 'Login required']);
                            break;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $result = $reviews->getUserReviews($user['id'], $limit, $offset);
                        echo json_encode($result);
                        break;
                        
                    case 'stats':
                        if (!isset($_GET['product_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID required']);
                            break;
                        }
                        
                        $result = $reviews->getReviewStats($_GET['product_id']);
                        echo json_encode($result);
                        break;
                        
                    case 'check':
                        if (!$user || !isset($_GET['product_id'])) {
                            echo json_encode(['success' => true, 'has_reviewed' => false]);
                            break;
                        }
                        
                        $hasReviewed = $reviews->hasUserReviewed($user['id'], $_GET['product_id']);
                        echo json_encode(['success' => true, 'has_reviewed' => $hasReviewed]);
                        break;
                        
                    case 'pending':
                        $auth->requireAdmin();
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $result = $reviews->getPendingReviews($limit, $offset);
                        echo json_encode($result);
                        break;
                        
                    case 'top_rated':
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                        $result = $reviews->getTopRatedProducts($limit);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Action required']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            if (!$user) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Login required']);
                break;
            }
            
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'add':
                        if (!isset($input['product_id']) || !isset($input['rating'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID and rating required']);
                            break;
                        }
                        
                        $title = $input['title'] ?? '';
                        $comment = $input['comment'] ?? '';
                        
                        $result = $reviews->addReview(
                            $user['id'],
                            $input['product_id'],
                            $input['rating'],
                            $comment,
                            $title
                        );
                        
                        echo json_encode($result);
                        break;
                        
                    case 'helpful':
                        if (!isset($input['review_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Review ID required']);
                            break;
                        }
                        
                        $isHelpful = isset($input['is_helpful']) ? (bool)$input['is_helpful'] : true;
                        
                        $result = $reviews->markReviewHelpful($input['review_id'], $user['id'], $isHelpful);
                        echo json_encode($result);
                        break;
                        
                    case 'moderate':
                        $auth->requireAdmin();
                        
                        if (!isset($input['review_id']) || !isset($input['status'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Review ID and status required']);
                            break;
                        }
                        
                        $result = $reviews->moderateReview($input['review_id'], $input['status'], $user['id']);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                // Default: add review
                if (!isset($input['product_id']) || !isset($input['rating'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Product ID and rating required']);
                    break;
                }
                
                $title = $input['title'] ?? '';
                $comment = $input['comment'] ?? '';
                
                $result = $reviews->addReview(
                    $user['id'],
                    $input['product_id'],
                    $input['rating'],
                    $comment,
                    $title
                );
                
                echo json_encode($result);
            }
            break;
            
        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            if (!$user) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Login required']);
                break;
            }
            
            if (!isset($input['review_id']) || !isset($input['rating'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Review ID and rating required']);
                break;
            }
            
            $title = $input['title'] ?? '';
            $comment = $input['comment'] ?? '';
            
            $result = $reviews->updateReview(
                $input['review_id'],
                $user['id'],
                $input['rating'],
                $comment,
                $title
            );
            
            echo json_encode($result);
            break;
            
        case 'DELETE':
            if (!$user) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Login required']);
                break;
            }
            
            if (!isset($_GET['review_id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Review ID required']);
                break;
            }
            
            $result = $reviews->deleteReview($_GET['review_id'], $user['id']);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
