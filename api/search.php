<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Search.php';
require_once '../includes/Auth.php';

try {
    $search = new Search();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    // Get current user
    $user = null;
    if ($auth->isLoggedIn()) {
        $user = $auth->getCurrentUser();
    }
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'search':
                        $query = $_GET['q'] ?? '';
                        $filters = [
                            'category_id' => $_GET['category'] ?? null,
                            'min_price' => $_GET['min_price'] ?? null,
                            'max_price' => $_GET['max_price'] ?? null,
                            'min_rating' => $_GET['min_rating'] ?? null,
                            'in_stock' => isset($_GET['in_stock']) ? (bool)$_GET['in_stock'] : null,
                            'on_sale' => isset($_GET['on_sale']) ? (bool)$_GET['on_sale'] : null,
                            'sort' => $_GET['sort'] ?? 'relevance'
                        ];
                        
                        // Remove null values
                        $filters = array_filter($filters, function($value) {
                            return $value !== null && $value !== '';
                        });
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $result = $search->searchProducts($query, $filters, $limit, $offset);
                        echo json_encode($result);
                        break;
                        
                    case 'suggestions':
                        if (!isset($_GET['q'])) {
                            echo json_encode(['success' => true, 'data' => []]);
                            break;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                        $result = $search->getSearchSuggestions($_GET['q'], $limit);
                        echo json_encode($result);
                        break;
                        
                    case 'popular':
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                        $result = $search->getPopularSearches($limit);
                        echo json_encode($result);
                        break;
                        
                    case 'history':
                        if (!$user) {
                            echo json_encode(['success' => true, 'data' => []]);
                            break;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                        $result = $search->getSearchHistory($user['id'], $limit);
                        echo json_encode($result);
                        break;
                        
                    case 'stats':
                        $auth->requireAdmin();
                        
                        $dateFrom = $_GET['date_from'] ?? null;
                        $dateTo = $_GET['date_to'] ?? null;
                        
                        $result = $search->getSearchStats($dateFrom, $dateTo);
                        echo json_encode($result);
                        break;
                        
                    case 'related':
                        if (!isset($_GET['product_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID required']);
                            break;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 6;
                        $result = $search->getRelatedProducts($_GET['product_id'], $limit);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                // Default search
                $query = $_GET['q'] ?? '';
                $filters = [
                    'category_id' => $_GET['category'] ?? null,
                    'min_price' => $_GET['min_price'] ?? null,
                    'max_price' => $_GET['max_price'] ?? null,
                    'min_rating' => $_GET['min_rating'] ?? null,
                    'in_stock' => isset($_GET['in_stock']) ? (bool)$_GET['in_stock'] : null,
                    'on_sale' => isset($_GET['on_sale']) ? (bool)$_GET['on_sale'] : null,
                    'sort' => $_GET['sort'] ?? 'relevance'
                ];
                
                // Remove null values
                $filters = array_filter($filters, function($value) {
                    return $value !== null && $value !== '';
                });
                
                $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                
                $result = $search->searchProducts($query, $filters, $limit, $offset);
                echo json_encode($result);
            }
            break;
            
        case 'DELETE':
            if (!$user) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Login required']);
                break;
            }
            
            if (isset($_GET['action']) && $_GET['action'] === 'clear_history') {
                $result = $search->clearSearchHistory($user['id']);
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
