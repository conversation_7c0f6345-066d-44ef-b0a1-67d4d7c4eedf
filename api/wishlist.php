<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/Database.php';
require_once '../includes/Wishlist.php';
require_once '../includes/Auth.php';

try {
    $wishlist = new Wishlist();
    $auth = new Auth();
    $method = $_SERVER['REQUEST_METHOD'];
    
    // Get current user
    $user = null;
    if ($auth->isLoggedIn()) {
        $user = $auth->getCurrentUser();
    }
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'list':
                        if (!$user) {
                            http_response_code(401);
                            echo json_encode(['success' => false, 'message' => 'Login required']);
                            break;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $result = $wishlist->getUserWishlist($user['id'], $limit, $offset);
                        echo json_encode($result);
                        break;
                        
                    case 'count':
                        if (!$user) {
                            echo json_encode(['success' => true, 'count' => 0]);
                            break;
                        }
                        
                        $count = $wishlist->getWishlistCount($user['id']);
                        echo json_encode(['success' => true, 'count' => $count]);
                        break;
                        
                    case 'check':
                        if (!$user || !isset($_GET['product_id'])) {
                            echo json_encode(['success' => true, 'in_wishlist' => false]);
                            break;
                        }
                        
                        $inWishlist = $wishlist->isInWishlist($user['id'], $_GET['product_id']);
                        echo json_encode(['success' => true, 'in_wishlist' => $inWishlist]);
                        break;
                        
                    case 'stats':
                        if (!$user) {
                            http_response_code(401);
                            echo json_encode(['success' => false, 'message' => 'Login required']);
                            break;
                        }
                        
                        $result = $wishlist->getWishlistStats($user['id']);
                        echo json_encode($result);
                        break;
                        
                    case 'popular':
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                        $result = $wishlist->getPopularWishlistItems($limit);
                        echo json_encode($result);
                        break;
                        
                    case 'shared':
                        if (!isset($_GET['token'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Share token required']);
                            break;
                        }
                        
                        $result = $wishlist->getSharedWishlist($_GET['token']);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                // Default: get user's wishlist
                if (!$user) {
                    http_response_code(401);
                    echo json_encode(['success' => false, 'message' => 'Login required']);
                    break;
                }
                
                $result = $wishlist->getUserWishlist($user['id']);
                echo json_encode($result);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                break;
            }
            
            if (!$user) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Login required']);
                break;
            }
            
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'add':
                        if (!isset($input['product_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID required']);
                            break;
                        }
                        
                        $result = $wishlist->addToWishlist($user['id'], $input['product_id']);
                        
                        if ($result['success']) {
                            $count = $wishlist->getWishlistCount($user['id']);
                            $result['wishlist_count'] = $count;
                        }
                        
                        echo json_encode($result);
                        break;
                        
                    case 'move_to_cart':
                        if (!isset($input['product_id'])) {
                            http_response_code(400);
                            echo json_encode(['success' => false, 'message' => 'Product ID required']);
                            break;
                        }
                        
                        $quantity = isset($input['quantity']) ? (int)$input['quantity'] : 1;
                        $result = $wishlist->moveToCart($user['id'], $input['product_id'], $quantity);
                        echo json_encode($result);
                        break;
                        
                    case 'share':
                        $isPublic = isset($input['is_public']) ? (bool)$input['is_public'] : false;
                        $result = $wishlist->shareWishlist($user['id'], $isPublic);
                        echo json_encode($result);
                        break;
                        
                    case 'clear':
                        $result = $wishlist->clearWishlist($user['id']);
                        echo json_encode($result);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid action']);
                }
            } else {
                // Default: add to wishlist
                if (!isset($input['product_id'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Product ID required']);
                    break;
                }
                
                $result = $wishlist->addToWishlist($user['id'], $input['product_id']);
                
                if ($result['success']) {
                    $count = $wishlist->getWishlistCount($user['id']);
                    $result['wishlist_count'] = $count;
                }
                
                echo json_encode($result);
            }
            break;
            
        case 'DELETE':
            if (!$user) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Login required']);
                break;
            }
            
            if (!isset($_GET['product_id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Product ID required']);
                break;
            }
            
            $result = $wishlist->removeFromWishlist($user['id'], $_GET['product_id']);
            
            if ($result['success']) {
                $count = $wishlist->getWishlistCount($user['id']);
                $result['wishlist_count'] = $count;
            }
            
            echo json_encode($result);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
