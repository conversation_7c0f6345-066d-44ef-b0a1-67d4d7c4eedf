<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="cart.html" class="active">Cart</a></li>
                    <li><a href="login.html">Login</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="cart-icon">
                    <a href="cart.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">0</span>
                    </a>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <section class="cart-page">
            <div class="container">
                <div class="cart-header">
                    <h1>Your Shopping Cart</h1>
                    <p>Review your items and proceed to checkout</p>
                </div>
                
                <div id="cart-content">
                    <!-- Cart items will be loaded here via JavaScript -->
                    <!-- Empty cart message (will be hidden when cart has items) -->
                    <div class="cart-empty" id="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <h2>Your cart is empty</h2>
                        <p>Looks like you haven't added any items to your cart yet.</p>
                        <a href="shop.html" class="btn">Start Shopping</a>
                    </div>
                    
                    <!-- Cart with items (will be shown when cart has items) -->
                    <div class="cart-with-items" id="cart-with-items" style="display: none;">
                        <div class="cart-table-container">
                            <table class="cart-table">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Total</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="cart-items">
                                    <!-- Cart items will be inserted here via JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="cart-actions">
                            <div class="cart-coupon">
                                <input type="text" placeholder="Coupon code">
                                <button class="btn-small">Apply Coupon</button>
                            </div>
                            <button class="btn-small update-cart">Update Cart</button>
                        </div>
                        
                        <div class="cart-checkout">
                            <div class="cart-summary">
                                <h2>Cart Summary</h2>
                                <div class="summary-item">
                                    <span>Subtotal</span>
                                    <span id="cart-subtotal">$0.00</span>
                                </div>
                                <div class="summary-item">
                                    <span>Shipping</span>
                                    <span id="cart-shipping">$0.00</span>
                                </div>
                                <div class="summary-item">
                                    <span>Tax</span>
                                    <span id="cart-tax">$0.00</span>
                                </div>
                                <div class="summary-item summary-total">
                                    <span>Total</span>
                                    <span id="cart-total">$0.00</span>
                                </div>
                                <a href="checkout.html" class="btn checkout-btn">Proceed to Checkout</a>
                                <a href="shop.html" class="continue-shopping">
                                    <i class="fas fa-arrow-left"></i> Continue Shopping
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="login.html">Account</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get cart from localStorage
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            
            // Update UI based on cart contents
            if (cart.length > 0) {
                document.getElementById('empty-cart').style.display = 'none';
                document.getElementById('cart-with-items').style.display = 'block';
                
                // Populate cart items
                const cartItemsContainer = document.getElementById('cart-items');
                let subtotal = 0;
                
                cart.forEach(item => {
                    const price = parseFloat(item.price.replace('$', ''));
                    const itemTotal = price * item.quantity;
                    subtotal += itemTotal;
                    
                    const itemRow = document.createElement('tr');
                    itemRow.innerHTML = `
                        <td>
                            <div class="cart-product">
                                <img src="${item.image}" alt="${item.name}">
                                <div class="cart-product-info">
                                    <h3>${item.name}</h3>
                                    <p>Size: Medium</p>
                                </div>
                            </div>
                        </td>
                        <td>${item.price}</td>
                        <td>
                            <div class="quantity-control">
                                <button class="quantity-btn decrease" data-id="${item.id}">-</button>
                                <input type="number" class="quantity-input" value="${item.quantity}" min="1" data-id="${item.id}">
                                <button class="quantity-btn increase" data-id="${item.id}">+</button>
                            </div>
                        </td>
                        <td>$${itemTotal.toFixed(2)}</td>
                        <td>
                            <button class="remove-btn" data-id="${item.id}">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    `;
                    
                    cartItemsContainer.appendChild(itemRow);
                });
                
                // Calculate and update summary
                const shipping = subtotal > 100 ? 0 : 5.99;
                const tax = subtotal * 0.1; // 10% tax
                const total = subtotal + shipping + tax;
                
                document.getElementById('cart-subtotal').textContent = `$${subtotal.toFixed(2)}`;
                document.getElementById('cart-shipping').textContent = shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`;
                document.getElementById('cart-tax').textContent = `$${tax.toFixed(2)}`;
                document.getElementById('cart-total').textContent = `$${total.toFixed(2)}`;
                
                // Add event listeners for quantity buttons
                document.querySelectorAll('.quantity-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const id = this.getAttribute('data-id');
                        const item = cart.find(item => item.id === id);
                        
                        if (this.classList.contains('increase')) {
                            item.quantity += 1;
                        } else if (this.classList.contains('decrease') && item.quantity > 1) {
                            item.quantity -= 1;
                        }
                        
                        localStorage.setItem('cart', JSON.stringify(cart));
                        location.reload(); // Refresh to update UI
                    });
                });
                
                // Add event listeners for remove buttons
                document.querySelectorAll('.remove-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const id = this.getAttribute('data-id');
                        const updatedCart = cart.filter(item => item.id !== id);
                        
                        localStorage.setItem('cart', JSON.stringify(updatedCart));
                        location.reload(); // Refresh to update UI
                    });
                });
            }
        });
    </script>
</body>
</html>