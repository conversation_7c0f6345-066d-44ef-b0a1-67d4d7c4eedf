<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .checkout-page {
            padding: 60px 0;
        }
        
        .checkout-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
        }
        
        .checkout-form {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .checkout-form h2,
        .order-summary h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .payment-methods {
            margin-top: 20px;
        }
        
        .payment-method {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover,
        .payment-method.active {
            border-color: var(--primary-color);
            background-color: rgba(106, 61, 232, 0.05);
        }
        
        .payment-method input {
            margin: 0;
        }
        
        .payment-method-info {
            flex: 1;
        }
        
        .payment-method-info h3 {
            font-size: 1rem;
            margin-bottom: 5px;
        }
        
        .payment-method-info p {
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .payment-icons {
            display: flex;
            gap: 10px;
        }
        
        .payment-icons i {
            font-size: 1.5rem;
            color: var(--text-color);
            opacity: 0.7;
        }
        
        .order-summary {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px var(--shadow-color);
            align-self: start;
        }
        
        .order-items {
            margin-bottom: 20px;
        }
        
        .order-item {
            display: flex;
            gap: 15px;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .order-item img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .order-item-info {
            flex: 1;
        }
        
        .order-item-info h3 {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .order-item-info p {
            font-size: 0.8rem;
            opacity: 0.7;
        }
        
        .order-item-price {
            font-weight: 600;
        }
        
        .place-order-btn {
            width: 100%;
            padding: 15px;
            font-size: 1.1rem;
            margin-top: 20px;
        }
        
        .terms {
            margin-top: 20px;
            font-size: 0.9rem;
            opacity: 0.8;
            text-align: center;
        }
        
        @media (max-width: 992px) {
            .checkout-container {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 576px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="cart.html">Cart</a></li>
                    <li><a href="login.html">Login</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="cart-icon">
                    <a href="cart.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">0</span>
                    </a>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <section class="checkout-page">
            <div class="container">
                <div class="checkout-container">
                    <div class="checkout-form">
                        <h2>Checkout</h2>
                        <form id="checkout-form">
                            <div class="form-section">
                                <h3>Shipping Information</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="first-name">First Name</label>
                                        <input type="text" id="first-name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="last-name">Last Name</label>
                                        <input type="text" id="last-name" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" required>
                                </div>
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" required>
                                </div>
                                <div class="form-group">
                                    <label for="address">Address</label>
                                    <input type="text" id="address" required>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="city">City</label>
                                        <input type="text" id="city" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="postal-code">Postal Code</label>
                                        <input type="text" id="postal-code" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="country">Country</label>
                                    <select id="country" required>
                                        <option value="">Select Country</option>
                                        <option value="us">United States</option>
                                        <option value="ca">Canada</option>
                                        <option value="uk">United Kingdom</option>
                                        <option value="au">Australia</option>
                                        <option value="jp">Japan</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h3>Payment Method</h3>
                                <div class="payment-methods">
                                    <div class="payment-method active">
                                        <input type="radio" name="payment" id="credit-card" checked>
                                        <div class="payment-method-info">
                                            <h3>Credit / Debit Card</h3>
                                            <p>Pay securely with your card</p>
                                        </div>
                                        <div class="payment-icons">
                                            <i class="fab fa-cc-visa"></i>
                                            <i class="fab fa-cc-mastercard"></i>
                                            <i class="fab fa-cc-amex"></i>
                                        </div>
                                    </div>
                                    
                                    <div class="payment-method">
                                        <input type="radio" name="payment" id="paypal">
                                        <div class="payment-method-info">
                                            <h3>PayPal</h3>
                                            <p>Pay with your PayPal account</p>
                                        </div>
                                        <div class="payment-icons">
                                            <i class="fab fa-paypal"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <div id="card-payment-form">
                                    <div class="form-group">
                                        <label for="card-number">Card Number</label>
                                        <input type="text" id="card-number" placeholder="1234 5678 9012 3456">
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="expiry-date">Expiry Date</label>
                                            <input type="text" id="expiry-date" placeholder="MM/YY">
                                        </div>
                                        <div class="form-group">
                                            <label for="cvv">CVV</label>
                                            <input type="text" id="cvv" placeholder="123">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="card-name">Name on Card</label>
                                        <input type="text" id="card-name">
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn place-order-btn">Place Order</button>
                            
                            <p class="terms">
                                By placing your order, you agree to our <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                            </p>
                        </form>
                    </div>
                    
                    <div class="order-summary">
                        <h2>Order Summary</h2>
                        <div class="order-items" id="order-items">
                            <!-- Order items will be loaded here via JavaScript -->
                        </div>
                        
                        <div class="summary-item">
                            <span>Subtotal</span>
                            <span id="order-subtotal">$0.00</span>
                        </div>
                        <div class="summary-item">
                            <span>Shipping</span>
                            <span id="order-shipping">$0.00</span>
                        </div>
                        <div class="summary-item">
                            <span>Tax</span>
                            <span id="order-tax">$0.00</span>
                        </div>
                        <div class="summary-item summary-total">
                            <span>Total</span>
                            <span id="order-total">$0.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="login.html">Account</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get cart from localStorage
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const orderItemsContainer = document.getElementById('order-items');
            
            // Calculate totals
            let subtotal = 0;
            
            // Populate order items
            cart.forEach(item => {
                const price = parseFloat(item.price.replace('$', ''));
                const itemTotal = price * item.quantity;
                subtotal += itemTotal;
                
                const orderItem = document.createElement('div');
                orderItem.className = 'order-item';
                orderItem.innerHTML = `
                    <img src="${item.image}" alt="${item.name}">
                    <div class="order-item-info">
                        <h3>${item.name}</h3>
                        <p>Quantity: ${item.quantity}</p>
                    </div>
                    <div class="order-item-price">
                        $${itemTotal.toFixed(2)}
                    </div>
                `;
                
                orderItemsContainer.appendChild(orderItem);
            });
            
            // Calculate and update summary
            const shipping = subtotal > 100 ? 0 : 5.99;
            const tax = subtotal * 0.1; // 10% tax
            const total = subtotal + shipping + tax;
            
            document.getElementById('order-subtotal').textContent = `$${subtotal.toFixed(2)}`;
            document.getElementById('order-shipping').textContent = shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`;
            document.getElementById('order-tax').textContent = `$${tax.toFixed(2)}`;
            document.getElementById('order-total').textContent = `$${total.toFixed(2)}`;
            
            // Handle payment method selection
            const paymentMethods = document.querySelectorAll('.payment-method');
            const cardPaymentForm = document.getElementById('card-payment-form');
            
            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    // Remove active class from all methods
                    paymentMethods.forEach(m => m.classList.remove('active'));
                    
                    // Add active class to clicked method
                    this.classList.add('active');
                    
                    // Check the radio button
                    this.querySelector('input[type="radio"]').checked = true;
                    
                    // Show/hide card payment form
                    if (this.querySelector('#credit-card')) {
                        cardPaymentForm.style.display = 'block';
                    } else {
                        cardPaymentForm.style.display = 'none';
                    }
                });
            });
            
            // Handle form submission
            document.getElementById('checkout-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                // In a real application, you would validate and process the payment here
                // For this demo, we'll just redirect to the confirmation page
                window.location.href = 'confirmation.html';
            });
        });
    </script>
</body>
</html>