/* Global Styles */
:root {
    /* Light Mode Colors */
    --primary-color: #6a3de8;
    --secondary-color: #ff6b6b;
    --accent-color: #ffbe0b;
    --text-color: #333333;
    --bg-color: #f5f5f5;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --footer-bg: #f0f0f0;
}

.dark-mode {
    /* Dark Mode Colors */
    --primary-color: #8a5cf5;
    --secondary-color: #ff8585;
    --accent-color: #ffd24c;
    --text-color: #e0e0e0;
    --bg-color: #121212;
    --card-bg: #1e1e1e;
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --footer-bg: #1a1a1a;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
}

.btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 12px 24px;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px var(--shadow-color);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
    border-radius: 20px;
}

.section-title {
    text-align: center;
    margin-bottom: 40px;
    font-size: 2.2rem;
    font-weight: 700;
    position: relative;
    padding-bottom: 15px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* Header Styles */
header {
    background-color: var(--card-bg);
    box-shadow: 0 2px 10px var(--shadow-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 15px 0;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: 1px;
}

.logo span {
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin: 0 15px;
}

.nav-links a {
    color: var(--text-color);
    font-weight: 500;
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.header-icons {
    display: flex;
    align-items: center;
}

.theme-toggle,
.cart-icon,
.mobile-menu {
    margin-left: 20px;
    cursor: pointer;
    font-size: 1.2rem;
    color: var(--text-color);
    transition: color 0.3s ease;
}

.theme-toggle:hover,
.cart-icon:hover,
.mobile-menu:hover {
    color: var(--primary-color);
}

.cart-icon {
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--secondary-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.mobile-menu {
    display: none;
}

/* Hero Section */
.hero {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(106, 61, 232, 0.1), rgba(255, 107, 107, 0.1));
    overflow: hidden;
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-content {
    flex: 1;
    padding-right: 40px;
}

.hero-content h2 {
    font-size: 3rem;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-content span {
    color: var(--primary-color);
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: var(--text-color);
    opacity: 0.8;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    position: relative;
}

.hero-image img {
    max-width: 100%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Featured Categories */
.featured-categories {
    padding: 80px 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.category-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px var(--shadow-color);
}

.category-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-bottom: 2px solid var(--primary-color);
}

.category-card h3 {
    padding: 15px 0;
    font-size: 1.2rem;
}

.category-card .btn-small {
    margin-bottom: 15px;
    background-color: var(--primary-color);
    color: white;
}

/* Trending Products */
.trending-products {
    padding: 80px 0;
    background-color: var(--bg-color);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.product-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px var(--shadow-color);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-tags {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.tag {
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    color: white;
}

.tag.new {
    background-color: var(--primary-color);
}

.tag.hot {
    background-color: var(--secondary-color);
}

.tag.sale {
    background-color: #2ecc71;
}

.tag.limited {
    background-color: var(--accent-color);
    color: #333;
}

.product-actions {
    position: absolute;
    bottom: -50px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 10px 0;
    background-color: rgba(0, 0, 0, 0.7);
    transition: bottom 0.3s ease;
}

.product-card:hover .product-actions {
    bottom: 0;
}

.product-actions button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.product-actions button:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.product-info {
    padding: 20px;
}

.product-info h3 {
    font-size: 1.1rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.old-price {
    font-size: 0.9rem;
    text-decoration: line-through;
    color: var(--text-color);
    opacity: 0.6;
}

.product-rating {
    display: flex;
    align-items: center;
}

.product-rating i {
    color: var(--accent-color);
    font-size: 0.9rem;
}

.product-rating span {
    margin-left: 5px;
    font-size: 0.8rem;
    color: var(--text-color);
    opacity: 0.7;
}

.view-more {
    text-align: center;
    margin-top: 50px;
}

/* Newsletter Section */
.newsletter {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.newsletter-content p {
    margin-bottom: 30px;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 30px 0 0 30px;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
}

.newsletter-form .btn {
    border-radius: 0 30px 30px 0;
    background-color: var(--accent-color);
    color: #333;
}

.newsletter-form .btn:hover {
    background-color: white;
}

/* Footer */
footer {
    background-color: var(--footer-bg);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

.footer-logo h2 {
    font-size: 1.8rem;
    margin-bottom: 15px;
}

.footer-logo span {
    color: var(--primary-color);
}

.footer-logo p {
    opacity: 0.8;
    line-height: 1.6;
}

.footer-links h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-links h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--primary-color);
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--text-color);
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 1;
    color: var(--primary-color);
}

.footer-contact p {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.footer-contact i {
    margin-right: 10px;
    color: var(--primary-color);
}

.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-icons a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: var(--card-bg);
    border-radius: 50%;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-5px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .categories-grid,
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
        position: fixed;
        top: 76px;
        left: 0;
        width: 100%;
        background-color: var(--card-bg);
        flex-direction: column;
        padding: 20px 0;
        box-shadow: 0 5px 15px var(--shadow-color);
        z-index: 1000;
    }

    .nav-links.active {
        display: flex;
    }

    .nav-links li {
        margin: 10px 0;
        text-align: center;
    }

    .mobile-menu {
        display: block;
    }

    .hero .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        padding-right: 0;
        margin-bottom: 40px;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .hero-content h2 {
        font-size: 2.2rem;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-form input {
        border-radius: 30px;
        margin-bottom: 10px;
    }

    .newsletter-form .btn {
        border-radius: 30px;
        width: 100%;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }
}

/* Shop Page Styles */
.shop-header {
    background: linear-gradient(135deg, rgba(106, 61, 232, 0.8), rgba(255, 107, 107, 0.8)), url('../images/shop-banner.jpg');
    background-size: cover;
    background-position: center;
    padding: 80px 0;
    text-align: center;
    color: white;
    margin-bottom: 40px;
}

.shop-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.shop-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 500;
}

.filter-group select {
    padding: 8px 15px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    font-family: 'Poppins', sans-serif;
}

.search-box {
    display: flex;
    align-items: center;
}

.search-box input {
    padding: 8px 15px;
    border-radius: 5px 0 0 5px;
    border: 1px solid var(--border-color);
    border-right: none;
    background-color: var(--card-bg);
    color: var(--text-color);
    font-family: 'Poppins', sans-serif;
}

.search-box button {
    padding: 8px 15px;
    border-radius: 0 5px 5px 0;
    border: 1px solid var(--primary-color);
    background-color: var(--primary-color);
    color: white;
    cursor: pointer;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 50px;
    gap: 10px;
}

.pagination button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination button.active,
.pagination button:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Cart Page Styles */
.cart-page {
    padding: 60px 0;
}

.cart-header {
    margin-bottom: 30px;
}

.cart-header h1 {
    font-size: 2.2rem;
    margin-bottom: 10px;
}

.cart-empty {
    text-align: center;
    padding: 50px 0;
}

.cart-empty i {
    font-size: 5rem;
    color: var(--border-color);
    margin-bottom: 20px;
}

.cart-empty p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.7;
}

.cart-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
}

.cart-table th {
    text-align: left;
    padding: 15px;
    background-color: var(--card-bg);
    border-bottom: 2px solid var(--primary-color);
}

.cart-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
}

.cart-product {
    display: flex;
    align-items: center;
    gap: 15px;
}

.cart-product img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 5px;
}

.cart-product-info h3 {
    font-size: 1rem;
    margin-bottom: 5px;
}

.cart-product-info p {
    font-size: 0.9rem;
    opacity: 0.7;
}

.quantity-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.quantity-input {
    width: 40px;
    height: 30px;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--card-bg);
    color: var(--text-color);
}

.remove-btn {
    background-color: transparent;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    transition: color 0.3s ease;
}

.remove-btn:hover {
    color: #ff3333;
}

.cart-summary {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.cart-summary h2 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.summary-total {
    font-size: 1.2rem;
    font-weight: 700;
    padding-top: 15px;
    margin-top: 15px;
    border-top: 1px solid var(--border-color);
}

.checkout-btn {
    width: 100%;
    margin-top: 20px;
    padding: 15px;
    font-size: 1.1rem;
}

.continue-shopping {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    color: var(--primary-color);
    font-weight: 500;
    transition: color 0.3s ease;
}

.continue-shopping:hover {
    color: var(--secondary-color);
}

/* Checkout Page Styles */
.checkout-page {
    padding: 60px 0;
}

.checkout-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
}

.checkout-form h2,
.order-summary h2 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    font-family: 'Poppins', sans-serif;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.payment-methods {
    margin-top: 30px;
}

.payment-method {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method:hover,
.payment-method.active {
    border-color: var(--primary-color);
    background-color: rgba(106, 61, 232, 0.05);
}

.payment-method input {
    margin: 0;
}

.payment-method-info {
    flex: 1;
}

.payment-method-info h3 {
    font-size: 1rem;
    margin-bottom: 5px;
}

.payment-method-info p {
    font-size: 0.9rem;
    opacity: 0.7;
}

.payment-icons {
    display: flex;
    gap: 10px;
}

.payment-icons i {
    font-size: 1.5rem;
    color: var(--text-color);
    opacity: 0.7;
}

.order-summary {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px var(--shadow-color);
    align-self: start;
}

.order-items {
    margin-bottom: 20px;
}

.order-item {
    display: flex;
    gap: 15px;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.order-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
}

.order-item-info {
    flex: 1;
}

.order-item-info h3 {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.order-item-info p {
    font-size: 0.8rem;
    opacity: 0.7;
}

.order-item-price {
    font-weight: 600;
}

.place-order-btn {
    width: 100%;
    margin-top: 20px;
    padding: 15px;
    font-size: 1.1rem;
}

/* Social Sharing Styles */
.share-section {
    margin: 30px 0;
    padding: 20px;
    background-color: var(--card-bg);
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.share-section h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.share-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.share-btn.whatsapp {
    background-color: #25D366;
}

.share-btn.facebook {
    background-color: #1877F2;
}

.share-btn.twitter {
    background-color: #1DA1F2;
}

.share-btn.instagram {
    background: linear-gradient(45deg, #F56040, #E1306C, #C13584, #833AB4);
}

.share-btn.linkedin {
    background-color: #0A66C2;
}

.share-btn.telegram {
    background-color: #0088CC;
}

.share-btn.copy-link {
    background-color: var(--primary-color);
}

.share-btn.email {
    background-color: #EA4335;
}

.product-share-btn {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.product-share-btn:hover {
    opacity: 1;
    background-color: var(--primary-color);
    color: white;
}

.share-trigger-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.share-trigger-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Share Modal */
.share-modal .modal-content {
    max-width: 500px;
}

.share-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.share-grid .share-btn {
    flex-direction: column;
    padding: 20px 15px;
    text-align: center;
    border-radius: 12px;
}

.share-grid .share-btn i {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.share-grid .share-btn span {
    font-size: 0.85rem;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 5px 15px var(--shadow-color);
    z-index: 10000;
    max-width: 350px;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.notification-content i {
    font-size: 1.2rem;
    margin-top: 2px;
}

.notification-success {
    border-left: 4px solid #48c774;
}

.notification-success i {
    color: #48c774;
}

.notification-error {
    border-left: 4px solid var(--secondary-color);
}

.notification-error i {
    color: var(--secondary-color);
}

.notification-info {
    border-left: 4px solid var(--primary-color);
}

.notification-info i {
    color: var(--primary-color);
}

.notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-color);
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.notification-close:hover {
    opacity: 1;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px var(--shadow-color);
}

.modal-header {
    padding: 20px 30px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
}

.close {
    color: var(--text-color);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 1;
}

.modal-body {
    padding: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--card-bg);
        flex-direction: column;
        padding: 20px 0;
        box-shadow: 0 5px 15px var(--shadow-color);
    }

    .nav-links.active {
        display: flex;
    }

    .mobile-menu {
        display: block;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .share-buttons {
        justify-content: center;
    }

    .share-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}