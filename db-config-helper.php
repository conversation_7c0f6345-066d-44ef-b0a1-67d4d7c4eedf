<?php
/**
 * Database Configuration Helper
 * Automatically detects common database configurations
 */

function detectDatabaseConfig() {
    $configs = [];
    
    // Check for MAMP
    if (is_dir('/Applications/MAMP')) {
        $configs[] = [
            'name' => 'MAMP',
            'host' => 'localhost',
            'port' => '8889',
            'user' => 'root',
            'pass' => 'root',
            'socket' => '/Applications/MAMP/tmp/mysql/mysql.sock'
        ];
    }
    
    // Check for XAMPP
    if (is_dir('/opt/lampp') || is_dir('C:\xampp')) {
        $configs[] = [
            'name' => 'XAMPP',
            'host' => 'localhost',
            'port' => '3306',
            'user' => 'root',
            'pass' => '',
            'socket' => '/opt/lampp/var/mysql/mysql.sock'
        ];
    }
    
    // Check for Homebrew MySQL
    if (file_exists('/usr/local/bin/mysql') || file_exists('/opt/homebrew/bin/mysql')) {
        $configs[] = [
            'name' => 'Homebrew MySQL',
            'host' => 'localhost',
            'port' => '3306',
            'user' => 'root',
            'pass' => '',
            'socket' => '/tmp/mysql.sock'
        ];
    }
    
    // Check for system MySQL
    if (file_exists('/usr/bin/mysql')) {
        $configs[] = [
            'name' => 'System MySQL',
            'host' => 'localhost',
            'port' => '3306',
            'user' => 'root',
            'pass' => '',
            'socket' => '/var/run/mysqld/mysqld.sock'
        ];
    }
    
    // Default configuration
    $configs[] = [
        'name' => 'Default',
        'host' => 'localhost',
        'port' => '3306',
        'user' => 'root',
        'pass' => '',
        'socket' => null
    ];
    
    return $configs;
}

function testConnection($config) {
    $dsns = [];
    
    // Add TCP connection
    $dsns[] = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
    
    // Add socket connection if available
    if ($config['socket'] && file_exists($config['socket'])) {
        $dsns[] = "mysql:unix_socket={$config['socket']};charset=utf8mb4";
    }
    
    foreach ($dsns as $dsn) {
        try {
            $pdo = new PDO($dsn, $config['user'], $config['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 3
            ]);
            
            $pdo->query("SELECT 1");
            return ['success' => true, 'dsn' => $dsn];
            
        } catch (PDOException $e) {
            continue;
        }
    }
    
    return ['success' => false, 'error' => 'Connection failed'];
}

$configs = detectDatabaseConfig();
$testResults = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_config'])) {
    $configIndex = (int)$_POST['config_index'];
    if (isset($configs[$configIndex])) {
        $testResults[$configIndex] = testConnection($configs[$configIndex]);
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Configuration Helper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .config-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background-color: #f9f9f9;
        }
        .config-card h3 {
            margin-top: 0;
            color: #333;
        }
        .config-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .config-detail {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .config-detail strong {
            display: block;
            color: #666;
            font-size: 0.9em;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .use-button {
            background-color: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .use-button:hover {
            background-color: #1e7e34;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Configuration Helper</h1>
        <p>This tool automatically detects common database configurations on your system.</p>
        
        <div class="info">
            <strong>💡 How to use:</strong><br>
            1. Test each configuration below to see which one works<br>
            2. Click "Use This Config" for the working configuration<br>
            3. You'll be redirected to the setup page with the correct settings
        </div>

        <?php foreach ($configs as $index => $config): ?>
            <div class="config-card">
                <h3><?= htmlspecialchars($config['name']) ?> Configuration</h3>
                
                <div class="config-details">
                    <div class="config-detail">
                        <strong>Host:</strong>
                        <?= htmlspecialchars($config['host']) ?>
                    </div>
                    <div class="config-detail">
                        <strong>Port:</strong>
                        <?= htmlspecialchars($config['port']) ?>
                    </div>
                    <div class="config-detail">
                        <strong>Username:</strong>
                        <?= htmlspecialchars($config['user']) ?>
                    </div>
                    <div class="config-detail">
                        <strong>Password:</strong>
                        <?= $config['pass'] ? str_repeat('*', strlen($config['pass'])) : '(empty)' ?>
                    </div>
                    <?php if ($config['socket']): ?>
                    <div class="config-detail">
                        <strong>Socket:</strong>
                        <?= htmlspecialchars($config['socket']) ?>
                        <?= file_exists($config['socket']) ? ' ✅' : ' ❌' ?>
                    </div>
                    <?php endif; ?>
                </div>

                <form method="POST" style="display: inline;">
                    <input type="hidden" name="config_index" value="<?= $index ?>">
                    <button type="submit" name="test_config" class="test-button">Test Connection</button>
                </form>

                <?php if (isset($testResults[$index])): ?>
                    <?php if ($testResults[$index]['success']): ?>
                        <div class="success">
                            ✅ Connection successful! DSN: <?= htmlspecialchars($testResults[$index]['dsn']) ?>
                        </div>
                        <a href="setup.php?db_host=<?= urlencode($config['host']) ?>&db_user=<?= urlencode($config['user']) ?>&db_pass=<?= urlencode($config['pass']) ?>" class="use-button">
                            Use This Config
                        </a>
                    <?php else: ?>
                        <div class="error">
                            ❌ <?= htmlspecialchars($testResults[$index]['error']) ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>

        <div class="info">
            <strong>🔧 Manual Configuration:</strong><br>
            If none of the above work, you can:<br>
            • <a href="test-db-connection.php">Use the detailed connection tester</a><br>
            • <a href="setup.php">Go to manual setup</a><br>
            • Check your database server documentation
        </div>

        <div class="info">
            <strong>📋 Common Issues:</strong><br>
            • <strong>Connection refused:</strong> Database server is not running<br>
            • <strong>Access denied:</strong> Wrong username/password<br>
            • <strong>No such file or directory:</strong> Wrong socket path or host<br>
            • <strong>Unknown database:</strong> Database doesn't exist (this is OK, we'll create it)
        </div>
    </div>
</body>
</html>
