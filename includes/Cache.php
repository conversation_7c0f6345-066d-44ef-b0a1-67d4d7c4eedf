<?php

class Cache {
    private static $instance = null;
    private $cacheDir;
    private $defaultTTL = 3600; // 1 hour
    
    private function __construct() {
        $this->cacheDir = __DIR__ . '/../cache/';
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function get($key) {
        $filename = $this->getFilename($key);
        
        if (!file_exists($filename)) {
            return null;
        }
        
        $data = file_get_contents($filename);
        $cache = json_decode($data, true);
        
        if (!$cache || !isset($cache['expires']) || !isset($cache['data'])) {
            $this->delete($key);
            return null;
        }
        
        // Check if expired
        if ($cache['expires'] < time()) {
            $this->delete($key);
            return null;
        }
        
        return $cache['data'];
    }
    
    public function set($key, $data, $ttl = null) {
        if ($ttl === null) {
            $ttl = $this->defaultTTL;
        }
        
        $cache = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        $filename = $this->getFilename($key);
        $result = file_put_contents($filename, json_encode($cache));
        
        return $result !== false;
    }
    
    public function delete($key) {
        $filename = $this->getFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }
    
    public function exists($key) {
        return $this->get($key) !== null;
    }
    
    public function clear() {
        $files = glob($this->cacheDir . '*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    public function clearExpired() {
        $files = glob($this->cacheDir . '*.cache');
        $deleted = 0;
        
        foreach ($files as $file) {
            $data = file_get_contents($file);
            $cache = json_decode($data, true);
            
            if (!$cache || !isset($cache['expires']) || $cache['expires'] < time()) {
                if (unlink($file)) {
                    $deleted++;
                }
            }
        }
        
        return $deleted;
    }
    
    public function remember($key, $callback, $ttl = null) {
        $data = $this->get($key);
        
        if ($data !== null) {
            return $data;
        }
        
        $data = $callback();
        $this->set($key, $data, $ttl);
        
        return $data;
    }
    
    public function increment($key, $value = 1) {
        $current = $this->get($key);
        
        if ($current === null) {
            $current = 0;
        }
        
        $new = $current + $value;
        $this->set($key, $new);
        
        return $new;
    }
    
    public function decrement($key, $value = 1) {
        return $this->increment($key, -$value);
    }
    
    public function getStats() {
        $files = glob($this->cacheDir . '*.cache');
        $totalSize = 0;
        $totalFiles = count($files);
        $expiredFiles = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $data = file_get_contents($file);
            $cache = json_decode($data, true);
            
            if (!$cache || !isset($cache['expires']) || $cache['expires'] < time()) {
                $expiredFiles++;
            }
        }
        
        return [
            'total_files' => $totalFiles,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'expired_files' => $expiredFiles,
            'cache_directory' => $this->cacheDir
        ];
    }
    
    public function getMultiple($keys) {
        $results = [];
        
        foreach ($keys as $key) {
            $results[$key] = $this->get($key);
        }
        
        return $results;
    }
    
    public function setMultiple($items, $ttl = null) {
        $results = [];
        
        foreach ($items as $key => $value) {
            $results[$key] = $this->set($key, $value, $ttl);
        }
        
        return $results;
    }
    
    public function deleteMultiple($keys) {
        $results = [];
        
        foreach ($keys as $key) {
            $results[$key] = $this->delete($key);
        }
        
        return $results;
    }
    
    public function flush() {
        return $this->clear();
    }
    
    public function getTTL($key) {
        $filename = $this->getFilename($key);
        
        if (!file_exists($filename)) {
            return null;
        }
        
        $data = file_get_contents($filename);
        $cache = json_decode($data, true);
        
        if (!$cache || !isset($cache['expires'])) {
            return null;
        }
        
        $ttl = $cache['expires'] - time();
        return $ttl > 0 ? $ttl : 0;
    }
    
    public function touch($key, $ttl = null) {
        $data = $this->get($key);
        
        if ($data === null) {
            return false;
        }
        
        return $this->set($key, $data, $ttl);
    }
    
    public function getKeys($pattern = '*') {
        $files = glob($this->cacheDir . $pattern . '.cache');
        $keys = [];
        
        foreach ($files as $file) {
            $basename = basename($file, '.cache');
            $keys[] = $basename;
        }
        
        return $keys;
    }
    
    private function getFilename($key) {
        $safeKey = preg_replace('/[^a-zA-Z0-9_-]/', '_', $key);
        return $this->cacheDir . $safeKey . '.cache';
    }
    
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    // Product-specific cache methods
    public function cacheProduct($productId, $productData, $ttl = 1800) {
        return $this->set("product_$productId", $productData, $ttl);
    }
    
    public function getProduct($productId) {
        return $this->get("product_$productId");
    }
    
    public function invalidateProduct($productId) {
        return $this->delete("product_$productId");
    }
    
    public function cacheProductList($key, $products, $ttl = 900) {
        return $this->set("products_$key", $products, $ttl);
    }
    
    public function getProductList($key) {
        return $this->get("products_$key");
    }
    
    // Category cache methods
    public function cacheCategories($categories, $ttl = 3600) {
        return $this->set('categories_all', $categories, $ttl);
    }
    
    public function getCategories() {
        return $this->get('categories_all');
    }
    
    public function invalidateCategories() {
        return $this->delete('categories_all');
    }
    
    // Search cache methods
    public function cacheSearchResults($query, $filters, $results, $ttl = 600) {
        $key = 'search_' . md5($query . serialize($filters));
        return $this->set($key, $results, $ttl);
    }
    
    public function getSearchResults($query, $filters) {
        $key = 'search_' . md5($query . serialize($filters));
        return $this->get($key);
    }
    
    // User session cache
    public function cacheUserSession($userId, $sessionData, $ttl = 1800) {
        return $this->set("user_session_$userId", $sessionData, $ttl);
    }
    
    public function getUserSession($userId) {
        return $this->get("user_session_$userId");
    }
    
    public function invalidateUserSession($userId) {
        return $this->delete("user_session_$userId");
    }
    
    // Cart cache
    public function cacheCart($sessionId, $cartData, $ttl = 3600) {
        return $this->set("cart_$sessionId", $cartData, $ttl);
    }
    
    public function getCart($sessionId) {
        return $this->get("cart_$sessionId");
    }
    
    public function invalidateCart($sessionId) {
        return $this->delete("cart_$sessionId");
    }
    
    // Analytics cache
    public function cacheAnalytics($key, $data, $ttl = 1800) {
        return $this->set("analytics_$key", $data, $ttl);
    }
    
    public function getAnalytics($key) {
        return $this->get("analytics_$key");
    }
    
    public function invalidateAnalytics($pattern = null) {
        if ($pattern) {
            $keys = $this->getKeys("analytics_$pattern");
        } else {
            $keys = $this->getKeys('analytics_*');
        }
        
        return $this->deleteMultiple($keys);
    }
    
    // Configuration cache
    public function cacheConfig($config, $ttl = 7200) {
        return $this->set('site_config', $config, $ttl);
    }
    
    public function getConfig() {
        return $this->get('site_config');
    }
    
    public function invalidateConfig() {
        return $this->delete('site_config');
    }
}
?>
