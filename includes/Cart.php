<?php

require_once 'Database.php';
require_once 'Product.php';

class Cart {
    private $db;
    private $sessionId;
    private $userId;
    
    public function __construct() {
        $this->db = Database::getInstance();
        
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $this->sessionId = session_id();
        $this->userId = $_SESSION['user_id'] ?? null;
    }
    
    public function addItem($productId, $quantity = 1) {
        try {
            // Check if product exists and is available
            $product = $this->db->fetch(
                "SELECT id, name, price, sale_price, stock_quantity, stock_status 
                 FROM products 
                 WHERE id = ? AND status = 'active'",
                [$productId]
            );
            
            if (!$product) {
                return ['success' => false, 'message' => 'Product not found'];
            }
            
            if ($product['stock_status'] !== 'in_stock' || $product['stock_quantity'] < $quantity) {
                return ['success' => false, 'message' => 'Product is out of stock'];
            }
            
            // Check if item already exists in cart
            $whereClause = $this->userId ? 'user_id = ? AND product_id = ?' : 'session_id = ? AND product_id = ?';
            $whereParams = $this->userId ? [$this->userId, $productId] : [$this->sessionId, $productId];
            
            $existingItem = $this->db->fetch(
                "SELECT * FROM cart WHERE $whereClause",
                $whereParams
            );
            
            if ($existingItem) {
                // Update quantity
                $newQuantity = $existingItem['quantity'] + $quantity;
                
                if ($newQuantity > $product['stock_quantity']) {
                    return ['success' => false, 'message' => 'Not enough stock available'];
                }
                
                $this->db->update('cart', 
                    ['quantity' => $newQuantity, 'updated_at' => date('Y-m-d H:i:s')],
                    'id = ?',
                    [$existingItem['id']]
                );
            } else {
                // Add new item
                $cartData = [
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'session_id' => $this->sessionId
                ];
                
                if ($this->userId) {
                    $cartData['user_id'] = $this->userId;
                }
                
                $this->db->insert('cart', $cartData);
            }
            
            return ['success' => true, 'message' => 'Item added to cart'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function updateItem($productId, $quantity) {
        try {
            if ($quantity <= 0) {
                return $this->removeItem($productId);
            }
            
            // Check stock availability
            $product = $this->db->fetch(
                "SELECT stock_quantity FROM products WHERE id = ? AND status = 'active'",
                [$productId]
            );
            
            if (!$product || $quantity > $product['stock_quantity']) {
                return ['success' => false, 'message' => 'Not enough stock available'];
            }
            
            $whereClause = $this->userId ? 'user_id = ? AND product_id = ?' : 'session_id = ? AND product_id = ?';
            $whereParams = $this->userId ? [$this->userId, $productId] : [$this->sessionId, $productId];
            
            $this->db->update('cart',
                ['quantity' => $quantity, 'updated_at' => date('Y-m-d H:i:s')],
                $whereClause,
                $whereParams
            );
            
            return ['success' => true, 'message' => 'Cart updated'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function removeItem($productId) {
        try {
            $whereClause = $this->userId ? 'user_id = ? AND product_id = ?' : 'session_id = ? AND product_id = ?';
            $whereParams = $this->userId ? [$this->userId, $productId] : [$this->sessionId, $productId];
            
            $this->db->delete('cart', $whereClause, $whereParams);
            
            return ['success' => true, 'message' => 'Item removed from cart'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function getItems() {
        $whereClause = $this->userId ? 'c.user_id = ?' : 'c.session_id = ?';
        $whereParams = $this->userId ? [$this->userId] : [$this->sessionId];
        
        $items = $this->db->fetchAll(
            "SELECT c.*, p.name, p.price, p.sale_price, p.stock_quantity, p.stock_status,
                    (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as image
             FROM cart c
             JOIN products p ON c.product_id = p.id
             WHERE $whereClause AND p.status = 'active'
             ORDER BY c.created_at DESC",
            $whereParams
        );
        
        // Calculate totals for each item
        foreach ($items as &$item) {
            $price = $item['sale_price'] ?? $item['price'];
            $item['unit_price'] = $price;
            $item['total_price'] = $price * $item['quantity'];
        }
        
        return $items;
    }
    
    public function getItemCount() {
        $whereClause = $this->userId ? 'user_id = ?' : 'session_id = ?';
        $whereParams = $this->userId ? [$this->userId] : [$this->sessionId];
        
        $result = $this->db->fetch(
            "SELECT SUM(quantity) as total_items FROM cart WHERE $whereClause",
            $whereParams
        );
        
        return (int)($result['total_items'] ?? 0);
    }
    
    public function getTotals() {
        $items = $this->getItems();
        
        $subtotal = 0;
        $totalItems = 0;
        
        foreach ($items as $item) {
            $subtotal += $item['total_price'];
            $totalItems += $item['quantity'];
        }
        
        // Get tax rate from settings
        $taxRate = $this->db->fetch("SELECT setting_value FROM site_settings WHERE setting_key = 'tax_rate'");
        $taxRate = $taxRate ? (float)$taxRate['setting_value'] : 0.08;
        
        // Get shipping cost
        $shippingCost = $this->db->fetch("SELECT setting_value FROM site_settings WHERE setting_key = 'shipping_cost'");
        $shippingCost = $shippingCost ? (float)$shippingCost['setting_value'] : 9.99;
        
        // Check for free shipping threshold
        $freeShippingThreshold = $this->db->fetch("SELECT setting_value FROM site_settings WHERE setting_key = 'free_shipping_threshold'");
        $freeShippingThreshold = $freeShippingThreshold ? (float)$freeShippingThreshold['setting_value'] : 50.00;
        
        if ($subtotal >= $freeShippingThreshold) {
            $shippingCost = 0;
        }
        
        $taxAmount = $subtotal * $taxRate;
        $total = $subtotal + $taxAmount + $shippingCost;
        
        return [
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'tax_rate' => $taxRate,
            'shipping_cost' => $shippingCost,
            'total' => $total,
            'item_count' => $totalItems,
            'free_shipping_threshold' => $freeShippingThreshold,
            'free_shipping_eligible' => $subtotal >= $freeShippingThreshold
        ];
    }
    
    public function clearCart() {
        try {
            $whereClause = $this->userId ? 'user_id = ?' : 'session_id = ?';
            $whereParams = $this->userId ? [$this->userId] : [$this->sessionId];
            
            $this->db->delete('cart', $whereClause, $whereParams);
            
            return ['success' => true, 'message' => 'Cart cleared'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function mergeCarts($userId) {
        try {
            $this->db->beginTransaction();
            
            // Get session cart items
            $sessionItems = $this->db->fetchAll(
                "SELECT * FROM cart WHERE session_id = ? AND user_id IS NULL",
                [$this->sessionId]
            );
            
            foreach ($sessionItems as $item) {
                // Check if user already has this product in cart
                $existingItem = $this->db->fetch(
                    "SELECT * FROM cart WHERE user_id = ? AND product_id = ?",
                    [$userId, $item['product_id']]
                );
                
                if ($existingItem) {
                    // Update quantity
                    $newQuantity = $existingItem['quantity'] + $item['quantity'];
                    $this->db->update('cart',
                        ['quantity' => $newQuantity],
                        'id = ?',
                        [$existingItem['id']]
                    );
                } else {
                    // Update session item with user ID
                    $this->db->update('cart',
                        ['user_id' => $userId],
                        'id = ?',
                        [$item['id']]
                    );
                }
            }
            
            // Remove any remaining session-only items
            $this->db->delete('cart', 'session_id = ? AND user_id IS NULL', [$this->sessionId]);
            
            $this->db->commit();
            $this->userId = $userId;
            
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function validateCart() {
        $items = $this->getItems();
        $errors = [];
        
        foreach ($items as $item) {
            if ($item['stock_status'] !== 'in_stock') {
                $errors[] = $item['name'] . ' is out of stock';
            } elseif ($item['quantity'] > $item['stock_quantity']) {
                $errors[] = $item['name'] . ' - only ' . $item['stock_quantity'] . ' available';
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    public function applyCoupon($couponCode) {
        try {
            // Get coupon details
            $coupon = $this->db->fetch(
                "SELECT * FROM coupons 
                 WHERE code = ? AND status = 'active' 
                 AND (start_date IS NULL OR start_date <= CURDATE()) 
                 AND (end_date IS NULL OR end_date >= CURDATE())",
                [$couponCode]
            );
            
            if (!$coupon) {
                return ['success' => false, 'message' => 'Invalid or expired coupon code'];
            }
            
            // Check usage limits
            if ($coupon['usage_limit'] && $coupon['used_count'] >= $coupon['usage_limit']) {
                return ['success' => false, 'message' => 'Coupon usage limit exceeded'];
            }
            
            // Check user usage limit
            if ($coupon['user_limit'] && $this->userId) {
                $userUsage = $this->db->fetch(
                    "SELECT COUNT(*) as usage_count FROM coupon_usage WHERE coupon_id = ? AND user_id = ?",
                    [$coupon['id'], $this->userId]
                );
                
                if ($userUsage['usage_count'] >= $coupon['user_limit']) {
                    return ['success' => false, 'message' => 'You have reached the usage limit for this coupon'];
                }
            }
            
            $totals = $this->getTotals();
            
            // Check minimum amount
            if ($coupon['minimum_amount'] > $totals['subtotal']) {
                return ['success' => false, 'message' => 'Minimum order amount of $' . number_format($coupon['minimum_amount'], 2) . ' required'];
            }
            
            // Calculate discount
            $discount = 0;
            if ($coupon['type'] === 'fixed') {
                $discount = $coupon['value'];
            } else { // percentage
                $discount = $totals['subtotal'] * ($coupon['value'] / 100);
            }
            
            // Apply maximum discount limit
            if ($coupon['maximum_discount'] && $discount > $coupon['maximum_discount']) {
                $discount = $coupon['maximum_discount'];
            }
            
            // Store coupon in session
            $_SESSION['applied_coupon'] = [
                'code' => $coupon['code'],
                'discount' => $discount,
                'coupon_id' => $coupon['id']
            ];
            
            return [
                'success' => true,
                'message' => 'Coupon applied successfully',
                'discount' => $discount
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function removeCoupon() {
        unset($_SESSION['applied_coupon']);
        return ['success' => true, 'message' => 'Coupon removed'];
    }
    
    public function getAppliedCoupon() {
        return $_SESSION['applied_coupon'] ?? null;
    }
}
