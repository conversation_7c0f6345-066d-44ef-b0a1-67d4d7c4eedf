<?php

require_once 'Database.php';

class Chatbot {
    private $db;
    private $apiKey;
    private $apiUrl;
    private $model;
    private $maxTokens;
    private $temperature;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->loadSettings();
    }
    
    private function loadSettings() {
        $settings = $this->db->fetch(
            "SELECT * FROM chatbot_settings WHERE id = 1"
        );
        
        if ($settings) {
            $this->apiKey = $settings['openrouter_api_key'];
            $this->apiUrl = $settings['api_url'] ?: 'https://openrouter.ai/api/v1/chat/completions';
            $this->model = $settings['model'] ?: 'meta-llama/llama-3.2-3b-instruct:free';
            $this->maxTokens = $settings['max_tokens'] ?: 500;
            $this->temperature = $settings['temperature'] ?: 0.7;
        } else {
            // Default settings
            $this->apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
            $this->model = 'meta-llama/llama-3.2-3b-instruct:free';
            $this->maxTokens = 500;
            $this->temperature = 0.7;
        }
    }
    
    public function sendMessage($message, $userId = null, $conversationId = null) {
        try {
            if (!$this->apiKey) {
                return [
                    'success' => false,
                    'message' => 'Chatbot is not configured. Please contact administrator.'
                ];
            }
            
            // Get conversation history
            $conversationHistory = $this->getConversationHistory($conversationId, $userId);
            
            // Prepare messages for API
            $messages = $this->prepareMessages($message, $conversationHistory);
            
            // Call OpenRouter API
            $response = $this->callOpenRouterAPI($messages);
            
            if ($response['success']) {
                $botResponse = $response['message'];
                
                // Save conversation
                $conversationId = $this->saveConversation($message, $botResponse, $userId, $conversationId);
                
                return [
                    'success' => true,
                    'message' => $botResponse,
                    'conversation_id' => $conversationId
                ];
            } else {
                return $response;
            }
            
        } catch (Exception $e) {
            error_log('Chatbot error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Sorry, I\'m having trouble responding right now. Please try again later.'
            ];
        }
    }
    
    private function prepareMessages($userMessage, $conversationHistory) {
        $messages = [];
        
        // System message with context about the store
        $systemMessage = $this->getSystemMessage();
        $messages[] = [
            'role' => 'system',
            'content' => $systemMessage
        ];
        
        // Add conversation history (last 10 messages to stay within token limits)
        $recentHistory = array_slice($conversationHistory, -10);
        foreach ($recentHistory as $msg) {
            $messages[] = [
                'role' => $msg['role'],
                'content' => $msg['content']
            ];
        }
        
        // Add current user message
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage
        ];
        
        return $messages;
    }
    
    private function getSystemMessage() {
        $storeInfo = $this->getStoreInfo();
        
        return "You are a helpful customer service assistant for Infinite Shadow, an anime merchandise store. 

Store Information:
- Store Name: Infinite Shadow
- Specializes in: Anime figures, posters, accessories, and collectibles
- Website: Premium anime merchandise with worldwide shipping
- Customer Service: Friendly, knowledgeable, and helpful

Your role:
- Help customers find products
- Answer questions about orders, shipping, and returns
- Provide product recommendations
- Assist with general inquiries
- Be enthusiastic about anime and collectibles
- Keep responses concise and helpful
- If you don't know specific details, direct them to contact support

Available product categories: Figures, Posters, Accessories, Clothing, Collectibles

Always be polite, professional, and show enthusiasm for anime culture. If asked about specific product availability or pricing, suggest they check the website or contact support for the most current information.";
    }
    
    private function getStoreInfo() {
        // Get basic store information from database
        $info = $this->db->fetch(
            "SELECT COUNT(*) as product_count FROM products WHERE status = 'active'"
        );
        
        $categories = $this->db->fetchAll(
            "SELECT name FROM categories WHERE status = 'active' ORDER BY name"
        );
        
        return [
            'product_count' => $info['product_count'] ?? 0,
            'categories' => array_column($categories, 'name')
        ];
    }
    
    private function callOpenRouterAPI($messages) {
        $data = [
            'model' => $this->model,
            'messages' => $messages,
            'max_tokens' => $this->maxTokens,
            'temperature' => $this->temperature,
            'stream' => false
        ];
        
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json',
            'HTTP-Referer: ' . ($_SERVER['HTTP_HOST'] ?? 'localhost'),
            'X-Title: Infinite Shadow Chatbot'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log('CURL Error: ' . $error);
            return [
                'success' => false,
                'message' => 'Connection error. Please try again.'
            ];
        }
        
        if ($httpCode !== 200) {
            error_log('API Error: HTTP ' . $httpCode . ' - ' . $response);
            return [
                'success' => false,
                'message' => 'Service temporarily unavailable. Please try again later.'
            ];
        }
        
        $responseData = json_decode($response, true);
        
        if (!$responseData || !isset($responseData['choices'][0]['message']['content'])) {
            error_log('Invalid API Response: ' . $response);
            return [
                'success' => false,
                'message' => 'Invalid response from service. Please try again.'
            ];
        }
        
        return [
            'success' => true,
            'message' => trim($responseData['choices'][0]['message']['content'])
        ];
    }
    
    private function getConversationHistory($conversationId, $userId) {
        if (!$conversationId) {
            return [];
        }
        
        $history = $this->db->fetchAll(
            "SELECT role, content FROM chatbot_messages 
             WHERE conversation_id = ? 
             ORDER BY created_at ASC",
            [$conversationId]
        );
        
        return $history ?: [];
    }
    
    private function saveConversation($userMessage, $botResponse, $userId, $conversationId) {
        try {
            $this->db->beginTransaction();
            
            // Create new conversation if needed
            if (!$conversationId) {
                $conversationData = [
                    'user_id' => $userId,
                    'title' => $this->generateConversationTitle($userMessage),
                    'status' => 'active'
                ];
                $conversationId = $this->db->insert('chatbot_conversations', $conversationData);
            }
            
            // Save user message
            $this->db->insert('chatbot_messages', [
                'conversation_id' => $conversationId,
                'role' => 'user',
                'content' => $userMessage,
                'user_id' => $userId
            ]);
            
            // Save bot response
            $this->db->insert('chatbot_messages', [
                'conversation_id' => $conversationId,
                'role' => 'assistant',
                'content' => $botResponse,
                'user_id' => $userId
            ]);
            
            // Update conversation last activity
            $this->db->update(
                'chatbot_conversations',
                ['updated_at' => date('Y-m-d H:i:s')],
                'id = ?',
                [$conversationId]
            );
            
            $this->db->commit();
            return $conversationId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log('Error saving conversation: ' . $e->getMessage());
            return $conversationId;
        }
    }
    
    private function generateConversationTitle($firstMessage) {
        $title = substr($firstMessage, 0, 50);
        if (strlen($firstMessage) > 50) {
            $title .= '...';
        }
        return $title;
    }
    
    public function getConversations($userId, $limit = 20) {
        if (!$userId) {
            return [];
        }
        
        return $this->db->fetchAll(
            "SELECT id, title, created_at, updated_at 
             FROM chatbot_conversations 
             WHERE user_id = ? AND status = 'active'
             ORDER BY updated_at DESC 
             LIMIT ?",
            [$userId, $limit]
        );
    }
    
    public function getConversationMessages($conversationId, $userId = null) {
        $sql = "SELECT cm.*, cc.title as conversation_title
                FROM chatbot_messages cm
                JOIN chatbot_conversations cc ON cm.conversation_id = cc.id
                WHERE cm.conversation_id = ?";
        
        $params = [$conversationId];
        
        if ($userId) {
            $sql .= " AND cc.user_id = ?";
            $params[] = $userId;
        }
        
        $sql .= " ORDER BY cm.created_at ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function deleteConversation($conversationId, $userId) {
        try {
            $this->db->beginTransaction();
            
            // Verify ownership
            $conversation = $this->db->fetch(
                "SELECT id FROM chatbot_conversations WHERE id = ? AND user_id = ?",
                [$conversationId, $userId]
            );
            
            if (!$conversation) {
                throw new Exception('Conversation not found');
            }
            
            // Delete messages
            $this->db->delete('chatbot_messages', 'conversation_id = ?', [$conversationId]);
            
            // Delete conversation
            $this->db->delete('chatbot_conversations', 'id = ?', [$conversationId]);
            
            $this->db->commit();
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function updateSettings($settings) {
        try {
            $allowedFields = [
                'openrouter_api_key', 'api_url', 'model', 
                'max_tokens', 'temperature', 'system_message',
                'enabled', 'welcome_message'
            ];
            
            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($settings[$field])) {
                    $updateData[$field] = $settings[$field];
                }
            }
            
            if (empty($updateData)) {
                return ['success' => false, 'message' => 'No valid fields to update'];
            }
            
            // Check if settings exist
            $existing = $this->db->fetch("SELECT id FROM chatbot_settings WHERE id = 1");
            
            if ($existing) {
                $this->db->update('chatbot_settings', $updateData, 'id = 1');
            } else {
                $updateData['id'] = 1;
                $this->db->insert('chatbot_settings', $updateData);
            }
            
            // Reload settings
            $this->loadSettings();
            
            return ['success' => true, 'message' => 'Settings updated successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function getSettings() {
        $settings = $this->db->fetch("SELECT * FROM chatbot_settings WHERE id = 1");
        
        if (!$settings) {
            return [
                'enabled' => false,
                'openrouter_api_key' => '',
                'api_url' => 'https://openrouter.ai/api/v1/chat/completions',
                'model' => 'meta-llama/llama-3.2-3b-instruct:free',
                'max_tokens' => 500,
                'temperature' => 0.7,
                'welcome_message' => 'Hello! I\'m here to help you with any questions about our anime merchandise. How can I assist you today?',
                'system_message' => ''
            ];
        }
        
        return $settings;
    }
    
    public function isEnabled() {
        $settings = $this->getSettings();
        return !empty($settings['enabled']) && !empty($settings['openrouter_api_key']);
    }
    
    public function getStats($dateFrom = null, $dateTo = null) {
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if ($dateFrom) {
            $whereClause .= " AND DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $whereClause .= " AND DATE(created_at) <= ?";
            $params[] = $dateTo;
        }
        
        // Total conversations and messages
        $totals = $this->db->fetch(
            "SELECT 
                (SELECT COUNT(*) FROM chatbot_conversations $whereClause) as total_conversations,
                (SELECT COUNT(*) FROM chatbot_messages $whereClause) as total_messages,
                (SELECT COUNT(*) FROM chatbot_messages $whereClause AND role = 'user') as user_messages,
                (SELECT COUNT(*) FROM chatbot_messages $whereClause AND role = 'assistant') as bot_messages",
            array_merge($params, $params, $params, $params)
        );
        
        // Messages per day
        $dailyStats = $this->db->fetchAll(
            "SELECT DATE(created_at) as date, COUNT(*) as count 
             FROM chatbot_messages $whereClause 
             GROUP BY DATE(created_at) 
             ORDER BY date DESC 
             LIMIT 30",
            $params
        );
        
        return [
            'totals' => $totals,
            'daily_stats' => $dailyStats
        ];
    }
}
