<?php

require_once 'Database.php';

class Coupon {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function getAllCoupons($filters = []) {
        $sql = "SELECT c.*, 
                       COUNT(cu.id) as usage_count_actual
                FROM coupons c 
                LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id 
                WHERE 1=1";
        
        $params = [];
        
        // Apply filters
        if (!empty($filters['status'])) {
            $sql .= " AND c.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['type'])) {
            $sql .= " AND c.type = ?";
            $params[] = $filters['type'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (c.code LIKE ? OR c.description LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($filters['active_only'])) {
            $sql .= " AND c.status = 'active'";
            $sql .= " AND (c.start_date IS NULL OR c.start_date <= CURDATE())";
            $sql .= " AND (c.end_date IS NULL OR c.end_date >= CURDATE())";
            $sql .= " AND (c.usage_limit IS NULL OR c.used_count < c.usage_limit)";
        }
        
        $sql .= " GROUP BY c.id";
        
        // Sorting
        $orderBy = " ORDER BY ";
        switch ($filters['sort'] ?? 'newest') {
            case 'oldest':
                $orderBy .= "c.created_at ASC";
                break;
            case 'code':
                $orderBy .= "c.code ASC";
                break;
            case 'value_high':
                $orderBy .= "c.value DESC";
                break;
            case 'value_low':
                $orderBy .= "c.value ASC";
                break;
            case 'usage':
                $orderBy .= "usage_count_actual DESC";
                break;
            default:
                $orderBy .= "c.created_at DESC";
        }
        
        $sql .= $orderBy;
        
        // Pagination
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT ?";
            $params[] = (int)$filters['limit'];
            
            if (!empty($filters['offset'])) {
                $sql .= " OFFSET ?";
                $params[] = (int)$filters['offset'];
            }
        }
        
        $coupons = $this->db->fetchAll($sql, $params);
        
        // Add additional info to each coupon
        foreach ($coupons as &$coupon) {
            $coupon['is_active'] = $this->isCouponActive($coupon);
            $coupon['is_expired'] = $this->isCouponExpired($coupon);
            $coupon['usage_percentage'] = $coupon['usage_limit'] ? 
                round(($coupon['usage_count_actual'] / $coupon['usage_limit']) * 100, 1) : 0;
        }
        
        return $coupons;
    }
    
    public function getCouponById($id) {
        $coupon = $this->db->fetch("SELECT * FROM coupons WHERE id = ?", [$id]);
        
        if ($coupon) {
            // Get actual usage count
            $usage = $this->db->fetch(
                "SELECT COUNT(*) as count FROM coupon_usage WHERE coupon_id = ?",
                [$id]
            );
            $coupon['usage_count_actual'] = $usage['count'];
            $coupon['is_active'] = $this->isCouponActive($coupon);
            $coupon['is_expired'] = $this->isCouponExpired($coupon);
        }
        
        return $coupon;
    }
    
    public function getCouponByCode($code) {
        $coupon = $this->db->fetch("SELECT * FROM coupons WHERE code = ?", [$code]);
        
        if ($coupon) {
            // Get actual usage count
            $usage = $this->db->fetch(
                "SELECT COUNT(*) as count FROM coupon_usage WHERE coupon_id = ?",
                [$coupon['id']]
            );
            $coupon['usage_count_actual'] = $usage['count'];
            $coupon['is_active'] = $this->isCouponActive($coupon);
            $coupon['is_expired'] = $this->isCouponExpired($coupon);
        }
        
        return $coupon;
    }
    
    public function createCoupon($data) {
        try {
            $this->db->beginTransaction();
            
            // Validate required fields
            if (empty($data['code'])) {
                throw new Exception('Coupon code is required');
            }
            
            if (empty($data['type']) || !in_array($data['type'], ['fixed', 'percentage'])) {
                throw new Exception('Valid coupon type is required');
            }
            
            if (!isset($data['value']) || $data['value'] <= 0) {
                throw new Exception('Coupon value must be greater than 0');
            }
            
            // Check if code already exists
            $existing = $this->db->fetch("SELECT id FROM coupons WHERE code = ?", [$data['code']]);
            if ($existing) {
                throw new Exception('Coupon code already exists');
            }
            
            // Validate percentage value
            if ($data['type'] === 'percentage' && $data['value'] > 100) {
                throw new Exception('Percentage value cannot exceed 100%');
            }
            
            // Prepare coupon data
            $couponData = [
                'code' => strtoupper(trim($data['code'])),
                'type' => $data['type'],
                'value' => (float)$data['value'],
                'minimum_amount' => isset($data['minimum_amount']) ? (float)$data['minimum_amount'] : 0,
                'maximum_discount' => isset($data['maximum_discount']) && $data['maximum_discount'] > 0 ? 
                    (float)$data['maximum_discount'] : null,
                'usage_limit' => isset($data['usage_limit']) && $data['usage_limit'] > 0 ? 
                    (int)$data['usage_limit'] : null,
                'user_limit' => isset($data['user_limit']) && $data['user_limit'] > 0 ? 
                    (int)$data['user_limit'] : null,
                'start_date' => !empty($data['start_date']) ? $data['start_date'] : null,
                'end_date' => !empty($data['end_date']) ? $data['end_date'] : null,
                'status' => isset($data['status']) ? $data['status'] : 'active',
                'description' => isset($data['description']) ? trim($data['description']) : null,
                'used_count' => 0
            ];
            
            // Validate dates
            if ($couponData['start_date'] && $couponData['end_date']) {
                if (strtotime($couponData['start_date']) > strtotime($couponData['end_date'])) {
                    throw new Exception('End date must be after start date');
                }
            }
            
            $couponId = $this->db->insert('coupons', $couponData);
            
            $this->db->commit();
            return ['success' => true, 'coupon_id' => $couponId];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function updateCoupon($id, $data) {
        try {
            $this->db->beginTransaction();
            
            // Check if coupon exists
            $coupon = $this->getCouponById($id);
            if (!$coupon) {
                throw new Exception('Coupon not found');
            }
            
            // Prepare update data
            $updateData = [];
            $allowedFields = ['code', 'type', 'value', 'minimum_amount', 'maximum_discount', 
                             'usage_limit', 'user_limit', 'start_date', 'end_date', 'status', 'description'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    if ($field === 'code') {
                        $newCode = strtoupper(trim($data[$field]));
                        // Check if new code already exists (excluding current coupon)
                        $existing = $this->db->fetch(
                            "SELECT id FROM coupons WHERE code = ? AND id != ?", 
                            [$newCode, $id]
                        );
                        if ($existing) {
                            throw new Exception('Coupon code already exists');
                        }
                        $updateData[$field] = $newCode;
                    } elseif (in_array($field, ['value', 'minimum_amount', 'maximum_discount'])) {
                        $updateData[$field] = $data[$field] > 0 ? (float)$data[$field] : null;
                    } elseif (in_array($field, ['usage_limit', 'user_limit'])) {
                        $updateData[$field] = $data[$field] > 0 ? (int)$data[$field] : null;
                    } elseif (in_array($field, ['start_date', 'end_date'])) {
                        $updateData[$field] = !empty($data[$field]) ? $data[$field] : null;
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }
            
            // Validate type and value
            if (isset($updateData['type']) && isset($updateData['value'])) {
                if ($updateData['type'] === 'percentage' && $updateData['value'] > 100) {
                    throw new Exception('Percentage value cannot exceed 100%');
                }
            } elseif (isset($updateData['type']) && $updateData['type'] === 'percentage' && $coupon['value'] > 100) {
                throw new Exception('Percentage value cannot exceed 100%');
            } elseif (isset($updateData['value']) && $coupon['type'] === 'percentage' && $updateData['value'] > 100) {
                throw new Exception('Percentage value cannot exceed 100%');
            }
            
            // Validate dates
            $startDate = $updateData['start_date'] ?? $coupon['start_date'];
            $endDate = $updateData['end_date'] ?? $coupon['end_date'];
            
            if ($startDate && $endDate && strtotime($startDate) > strtotime($endDate)) {
                throw new Exception('End date must be after start date');
            }
            
            if (!empty($updateData)) {
                $this->db->update('coupons', $updateData, 'id = ?', [$id]);
            }
            
            $this->db->commit();
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function deleteCoupon($id) {
        try {
            $this->db->beginTransaction();
            
            // Check if coupon exists
            $coupon = $this->getCouponById($id);
            if (!$coupon) {
                throw new Exception('Coupon not found');
            }
            
            // Check if coupon has been used
            $usageCount = $this->db->fetch(
                "SELECT COUNT(*) as count FROM coupon_usage WHERE coupon_id = ?",
                [$id]
            );
            
            if ($usageCount['count'] > 0) {
                // Don't delete, just deactivate
                $this->db->update('coupons', ['status' => 'inactive'], 'id = ?', [$id]);
                $this->db->commit();
                return ['success' => true, 'message' => 'Coupon deactivated (has usage history)'];
            } else {
                // Safe to delete
                $this->db->delete('coupons', 'id = ?', [$id]);
                $this->db->commit();
                return ['success' => true, 'message' => 'Coupon deleted successfully'];
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function validateCoupon($code, $cartTotal = 0, $userId = null) {
        $coupon = $this->getCouponByCode($code);
        
        if (!$coupon) {
            return ['valid' => false, 'message' => 'Invalid coupon code'];
        }
        
        if ($coupon['status'] !== 'active') {
            return ['valid' => false, 'message' => 'Coupon is not active'];
        }
        
        // Check date validity
        if ($coupon['start_date'] && strtotime($coupon['start_date']) > time()) {
            return ['valid' => false, 'message' => 'Coupon is not yet valid'];
        }
        
        if ($coupon['end_date'] && strtotime($coupon['end_date']) < time()) {
            return ['valid' => false, 'message' => 'Coupon has expired'];
        }
        
        // Check usage limits
        if ($coupon['usage_limit'] && $coupon['usage_count_actual'] >= $coupon['usage_limit']) {
            return ['valid' => false, 'message' => 'Coupon usage limit exceeded'];
        }
        
        // Check user usage limit
        if ($coupon['user_limit'] && $userId) {
            $userUsage = $this->db->fetch(
                "SELECT COUNT(*) as count FROM coupon_usage WHERE coupon_id = ? AND user_id = ?",
                [$coupon['id'], $userId]
            );
            
            if ($userUsage['count'] >= $coupon['user_limit']) {
                return ['valid' => false, 'message' => 'You have reached the usage limit for this coupon'];
            }
        }
        
        // Check minimum amount
        if ($coupon['minimum_amount'] > $cartTotal) {
            return [
                'valid' => false, 
                'message' => 'Minimum order amount of $' . number_format($coupon['minimum_amount'], 2) . ' required'
            ];
        }
        
        // Calculate discount
        $discount = 0;
        if ($coupon['type'] === 'fixed') {
            $discount = $coupon['value'];
        } else { // percentage
            $discount = $cartTotal * ($coupon['value'] / 100);
        }
        
        // Apply maximum discount limit
        if ($coupon['maximum_discount'] && $discount > $coupon['maximum_discount']) {
            $discount = $coupon['maximum_discount'];
        }
        
        // Ensure discount doesn't exceed cart total
        $discount = min($discount, $cartTotal);
        
        return [
            'valid' => true,
            'coupon' => $coupon,
            'discount' => $discount,
            'message' => 'Coupon applied successfully'
        ];
    }
    
    public function getCouponUsage($couponId, $limit = 50, $offset = 0) {
        return $this->db->fetchAll(
            "SELECT cu.*, u.first_name, u.last_name, u.email, o.order_number, o.total_amount
             FROM coupon_usage cu
             LEFT JOIN users u ON cu.user_id = u.id
             LEFT JOIN orders o ON cu.order_id = o.id
             WHERE cu.coupon_id = ?
             ORDER BY cu.used_at DESC
             LIMIT ? OFFSET ?",
            [$couponId, $limit, $offset]
        );
    }
    
    public function getCouponStats($dateFrom = null, $dateTo = null) {
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if ($dateFrom) {
            $whereClause .= " AND DATE(c.created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $whereClause .= " AND DATE(c.created_at) <= ?";
            $params[] = $dateTo;
        }
        
        // Total coupons and usage
        $totals = $this->db->fetch(
            "SELECT COUNT(*) as total_coupons,
                    SUM(c.used_count) as total_usage,
                    AVG(c.value) as average_value
             FROM coupons c $whereClause",
            $params
        );
        
        // Coupons by status
        $statusStats = $this->db->fetchAll(
            "SELECT status, COUNT(*) as count 
             FROM coupons c $whereClause 
             GROUP BY status",
            $params
        );
        
        // Coupons by type
        $typeStats = $this->db->fetchAll(
            "SELECT type, COUNT(*) as count, AVG(value) as avg_value 
             FROM coupons c $whereClause 
             GROUP BY type",
            $params
        );
        
        return [
            'totals' => $totals,
            'status_breakdown' => $statusStats,
            'type_breakdown' => $typeStats
        ];
    }
    
    public function generateCouponCode($prefix = '', $length = 8) {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code = $prefix;
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        // Check if code already exists
        $existing = $this->db->fetch("SELECT id FROM coupons WHERE code = ?", [$code]);
        
        if ($existing) {
            // Recursively generate a new code
            return $this->generateCouponCode($prefix, $length);
        }
        
        return $code;
    }
    
    private function isCouponActive($coupon) {
        if ($coupon['status'] !== 'active') return false;
        
        $now = time();
        
        if ($coupon['start_date'] && strtotime($coupon['start_date']) > $now) return false;
        if ($coupon['end_date'] && strtotime($coupon['end_date']) < $now) return false;
        if ($coupon['usage_limit'] && $coupon['used_count'] >= $coupon['usage_limit']) return false;
        
        return true;
    }
    
    private function isCouponExpired($coupon) {
        if ($coupon['end_date'] && strtotime($coupon['end_date']) < time()) return true;
        if ($coupon['usage_limit'] && $coupon['used_count'] >= $coupon['usage_limit']) return true;
        
        return false;
    }
}
