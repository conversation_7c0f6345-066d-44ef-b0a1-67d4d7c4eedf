<?php

require_once 'Database.php';

class FileManager {
    private $db;
    private $uploadDir;
    private $allowedTypes;
    private $maxFileSize;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->uploadDir = 'uploads/files/';
        $this->allowedTypes = [
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
            'document' => ['pdf', 'doc', 'docx', 'txt', 'rtf'],
            'spreadsheet' => ['xls', 'xlsx', 'csv'],
            'presentation' => ['ppt', 'pptx'],
            'archive' => ['zip', 'rar', '7z', 'tar', 'gz'],
            'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
            'audio' => ['mp3', 'wav', 'ogg', 'aac', 'flac']
        ];
        $this->maxFileSize = 50 * 1024 * 1024; // 50MB
        
        // Create upload directory if it doesn't exist
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    public function uploadFile($file, $uploadedBy = null, $description = '') {
        try {
            // Validate file
            $validation = $this->validateFile($file);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // Generate unique filename
            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = uniqid() . '_' . time() . '.' . $fileExtension;
            $filePath = $this->uploadDir . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                return ['success' => false, 'message' => 'Failed to move uploaded file'];
            }
            
            // Get file info
            $fileInfo = $this->getFileInfo($filePath, $file);
            
            // Save to database
            $fileData = [
                'filename' => $filename,
                'original_filename' => $file['name'],
                'file_path' => $filePath,
                'file_size' => $file['size'],
                'file_type' => $fileInfo['type'],
                'mime_type' => $file['type'],
                'uploaded_by' => $uploadedBy,
                'description' => $description,
                'is_public' => true
            ];
            
            $fileId = $this->db->insert('files', $fileData);
            
            // Create thumbnail for images
            if ($fileInfo['type'] === 'image') {
                $this->createThumbnail($filePath, $filename);
            }
            
            return [
                'success' => true,
                'file_id' => $fileId,
                'filename' => $filename,
                'original_filename' => $file['name'],
                'file_path' => $filePath,
                'file_url' => $this->getFileUrl($filename),
                'file_type' => $fileInfo['type'],
                'file_size' => $file['size']
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function uploadMultipleFiles($files, $uploadedBy = null) {
        $results = [];
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($files['tmp_name'] as $index => $tmpName) {
            if ($files['error'][$index] === UPLOAD_ERR_OK) {
                $file = [
                    'name' => $files['name'][$index],
                    'type' => $files['type'][$index],
                    'tmp_name' => $tmpName,
                    'error' => $files['error'][$index],
                    'size' => $files['size'][$index]
                ];
                
                $result = $this->uploadFile($file, $uploadedBy);
                $results[] = $result;
                
                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            } else {
                $results[] = [
                    'success' => false,
                    'message' => 'Upload error for file: ' . $files['name'][$index]
                ];
                $errorCount++;
            }
        }
        
        return [
            'success' => $successCount > 0,
            'results' => $results,
            'summary' => [
                'total' => count($files['tmp_name']),
                'success' => $successCount,
                'errors' => $errorCount
            ]
        ];
    }
    
    public function getAllFiles($filters = []) {
        $sql = "SELECT f.*, u.first_name, u.last_name 
                FROM files f 
                LEFT JOIN users u ON f.uploaded_by = u.id 
                WHERE 1=1";
        
        $params = [];
        
        // Apply filters
        if (!empty($filters['file_type'])) {
            $sql .= " AND f.file_type = ?";
            $params[] = $filters['file_type'];
        }
        
        if (!empty($filters['uploaded_by'])) {
            $sql .= " AND f.uploaded_by = ?";
            $params[] = $filters['uploaded_by'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (f.original_filename LIKE ? OR f.description LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (isset($filters['is_public'])) {
            $sql .= " AND f.is_public = ?";
            $params[] = $filters['is_public'] ? 1 : 0;
        }
        
        // Sorting
        $orderBy = " ORDER BY ";
        switch ($filters['sort'] ?? 'newest') {
            case 'oldest':
                $orderBy .= "f.created_at ASC";
                break;
            case 'name':
                $orderBy .= "f.original_filename ASC";
                break;
            case 'size_large':
                $orderBy .= "f.file_size DESC";
                break;
            case 'size_small':
                $orderBy .= "f.file_size ASC";
                break;
            default:
                $orderBy .= "f.created_at DESC";
        }
        
        $sql .= $orderBy;
        
        // Pagination
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT ?";
            $params[] = (int)$filters['limit'];
            
            if (!empty($filters['offset'])) {
                $sql .= " OFFSET ?";
                $params[] = (int)$filters['offset'];
            }
        }
        
        $files = $this->db->fetchAll($sql, $params);
        
        // Add additional info to each file
        foreach ($files as &$file) {
            $file['file_url'] = $this->getFileUrl($file['filename']);
            $file['thumbnail_url'] = $this->getThumbnailUrl($file['filename'], $file['file_type']);
            $file['formatted_size'] = $this->formatFileSize($file['file_size']);
            $file['uploader_name'] = $file['first_name'] && $file['last_name'] ? 
                $file['first_name'] . ' ' . $file['last_name'] : 'Unknown';
        }
        
        return $files;
    }
    
    public function getFileById($fileId) {
        $file = $this->db->fetch("SELECT * FROM files WHERE id = ?", [$fileId]);
        
        if ($file) {
            $file['file_url'] = $this->getFileUrl($file['filename']);
            $file['thumbnail_url'] = $this->getThumbnailUrl($file['filename'], $file['file_type']);
            $file['formatted_size'] = $this->formatFileSize($file['file_size']);
        }
        
        return $file;
    }
    
    public function deleteFile($fileId) {
        try {
            $file = $this->getFileById($fileId);
            
            if (!$file) {
                return ['success' => false, 'message' => 'File not found'];
            }
            
            // Delete physical file
            if (file_exists($file['file_path'])) {
                unlink($file['file_path']);
            }
            
            // Delete thumbnail if exists
            $thumbnailPath = $this->uploadDir . 'thumbs/thumb_' . $file['filename'];
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }
            
            // Delete from database
            $this->db->delete('files', 'id = ?', [$fileId]);
            
            return ['success' => true, 'message' => 'File deleted successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function updateFile($fileId, $data) {
        try {
            $allowedFields = ['description', 'is_public'];
            $updateData = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            if (empty($updateData)) {
                return ['success' => false, 'message' => 'No valid fields to update'];
            }
            
            $this->db->update('files', $updateData, 'id = ?', [$fileId]);
            
            return ['success' => true, 'message' => 'File updated successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    private function validateFile($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['valid' => false, 'message' => 'File upload error'];
        }
        
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            return ['valid' => false, 'message' => 'File size exceeds maximum allowed size'];
        }
        
        // Check file extension
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $isAllowed = false;
        
        foreach ($this->allowedTypes as $category => $extensions) {
            if (in_array($fileExtension, $extensions)) {
                $isAllowed = true;
                break;
            }
        }
        
        if (!$isAllowed) {
            return ['valid' => false, 'message' => 'File type not allowed'];
        }
        
        return ['valid' => true];
    }
    
    private function getFileInfo($filePath, $file) {
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        foreach ($this->allowedTypes as $category => $extensions) {
            if (in_array($fileExtension, $extensions)) {
                return ['type' => $category, 'extension' => $fileExtension];
            }
        }
        
        return ['type' => 'unknown', 'extension' => $fileExtension];
    }
    
    private function createThumbnail($filePath, $filename) {
        $thumbnailDir = $this->uploadDir . 'thumbs/';
        
        if (!is_dir($thumbnailDir)) {
            mkdir($thumbnailDir, 0755, true);
        }
        
        $thumbnailPath = $thumbnailDir . 'thumb_' . $filename;
        
        // Use the Utils class resize function
        Utils::resizeImage($filePath, $thumbnailPath, 200, 200, 80);
    }
    
    public function getFileUrl($filename) {
        return $this->uploadDir . $filename;
    }
    
    public function getThumbnailUrl($filename, $fileType) {
        if ($fileType === 'image') {
            $thumbnailPath = $this->uploadDir . 'thumbs/thumb_' . $filename;
            if (file_exists($thumbnailPath)) {
                return $thumbnailPath;
            }
        }
        
        // Return default icon based on file type
        return $this->getDefaultIcon($fileType);
    }
    
    private function getDefaultIcon($fileType) {
        $icons = [
            'document' => 'fas fa-file-alt',
            'spreadsheet' => 'fas fa-file-excel',
            'presentation' => 'fas fa-file-powerpoint',
            'archive' => 'fas fa-file-archive',
            'video' => 'fas fa-file-video',
            'audio' => 'fas fa-file-audio',
            'image' => 'fas fa-file-image'
        ];
        
        return $icons[$fileType] ?? 'fas fa-file';
    }
    
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    public function getStorageStats() {
        $stats = $this->db->fetch(
            "SELECT 
                COUNT(*) as total_files,
                SUM(file_size) as total_size,
                AVG(file_size) as average_size
             FROM files"
        );
        
        $typeStats = $this->db->fetchAll(
            "SELECT file_type, COUNT(*) as count, SUM(file_size) as size 
             FROM files 
             GROUP BY file_type 
             ORDER BY count DESC"
        );
        
        return [
            'total_files' => $stats['total_files'] ?? 0,
            'total_size' => $stats['total_size'] ?? 0,
            'total_size_formatted' => $this->formatFileSize($stats['total_size'] ?? 0),
            'average_size' => $stats['average_size'] ?? 0,
            'average_size_formatted' => $this->formatFileSize($stats['average_size'] ?? 0),
            'by_type' => $typeStats
        ];
    }
}
