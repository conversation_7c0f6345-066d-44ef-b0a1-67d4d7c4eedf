<?php

require_once 'Database.php';

class Logger {
    private $db;
    private static $instance = null;
    
    private function __construct() {
        $this->db = Database::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function log($level, $message, $context = []) {
        try {
            $this->db->insert('system_logs', [
                'level' => $level,
                'message' => $message,
                'context' => json_encode($context),
                'ip_address' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'user_id' => $context['user_id'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Fallback to error_log if database logging fails
            error_log("Logger Error: " . $e->getMessage());
            error_log("Original Log: [$level] $message " . json_encode($context));
        }
    }
    
    public function emergency($message, $context = []) {
        $this->log('emergency', $message, $context);
    }
    
    public function alert($message, $context = []) {
        $this->log('alert', $message, $context);
    }
    
    public function critical($message, $context = []) {
        $this->log('critical', $message, $context);
    }
    
    public function error($message, $context = []) {
        $this->log('error', $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log('warning', $message, $context);
    }
    
    public function notice($message, $context = []) {
        $this->log('notice', $message, $context);
    }
    
    public function info($message, $context = []) {
        $this->log('info', $message, $context);
    }
    
    public function debug($message, $context = []) {
        $this->log('debug', $message, $context);
    }
    
    public function logUserAction($userId, $action, $details = []) {
        $this->info("User action: $action", [
            'user_id' => $userId,
            'action' => $action,
            'details' => $details
        ]);
    }
    
    public function logSecurityEvent($event, $details = []) {
        $this->warning("Security event: $event", [
            'event' => $event,
            'details' => $details,
            'ip' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }
    
    public function logAPIRequest($endpoint, $method, $responseCode, $responseTime = null) {
        $this->info("API request", [
            'endpoint' => $endpoint,
            'method' => $method,
            'response_code' => $responseCode,
            'response_time' => $responseTime,
            'ip' => $this->getClientIP()
        ]);
    }
    
    public function logDatabaseQuery($query, $executionTime = null, $error = null) {
        $level = $error ? 'error' : 'debug';
        $message = $error ? "Database query failed: $error" : "Database query executed";
        
        $this->log($level, $message, [
            'query' => $query,
            'execution_time' => $executionTime,
            'error' => $error
        ]);
    }
    
    public function logFileOperation($operation, $filename, $success = true, $error = null) {
        $level = $success ? 'info' : 'error';
        $message = $success ? "File operation successful: $operation" : "File operation failed: $operation";
        
        $this->log($level, $message, [
            'operation' => $operation,
            'filename' => $filename,
            'success' => $success,
            'error' => $error
        ]);
    }
    
    public function logEmailSent($to, $subject, $success = true, $error = null) {
        $level = $success ? 'info' : 'error';
        $message = $success ? "Email sent successfully" : "Email sending failed";
        
        $this->log($level, $message, [
            'to' => $to,
            'subject' => $subject,
            'success' => $success,
            'error' => $error
        ]);
    }
    
    public function logPaymentEvent($orderId, $event, $amount = null, $details = []) {
        $this->info("Payment event: $event", [
            'order_id' => $orderId,
            'event' => $event,
            'amount' => $amount,
            'details' => $details
        ]);
    }
    
    public function getLogs($level = null, $limit = 100, $offset = 0, $dateFrom = null, $dateTo = null) {
        try {
            $whereConditions = [];
            $params = [];
            
            if ($level) {
                $whereConditions[] = "level = ?";
                $params[] = $level;
            }
            
            if ($dateFrom) {
                $whereConditions[] = "DATE(created_at) >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $whereConditions[] = "DATE(created_at) <= ?";
                $params[] = $dateTo;
            }
            
            $whereClause = empty($whereConditions) ? "" : "WHERE " . implode(" AND ", $whereConditions);
            
            $sql = "SELECT * FROM system_logs $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $logs = $this->db->fetchAll($sql, $params);
            
            // Decode context JSON
            foreach ($logs as &$log) {
                $log['context'] = json_decode($log['context'], true) ?: [];
            }
            
            return ['success' => true, 'data' => $logs];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error retrieving logs'];
        }
    }
    
    public function getLogStats($dateFrom = null, $dateTo = null) {
        try {
            $whereConditions = [];
            $params = [];
            
            if ($dateFrom) {
                $whereConditions[] = "DATE(created_at) >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $whereConditions[] = "DATE(created_at) <= ?";
                $params[] = $dateTo;
            }
            
            $whereClause = empty($whereConditions) ? "" : "WHERE " . implode(" AND ", $whereConditions);
            
            $stats = $this->db->fetch(
                "SELECT 
                    COUNT(*) as total_logs,
                    SUM(CASE WHEN level = 'emergency' THEN 1 ELSE 0 END) as emergency_count,
                    SUM(CASE WHEN level = 'alert' THEN 1 ELSE 0 END) as alert_count,
                    SUM(CASE WHEN level = 'critical' THEN 1 ELSE 0 END) as critical_count,
                    SUM(CASE WHEN level = 'error' THEN 1 ELSE 0 END) as error_count,
                    SUM(CASE WHEN level = 'warning' THEN 1 ELSE 0 END) as warning_count,
                    SUM(CASE WHEN level = 'notice' THEN 1 ELSE 0 END) as notice_count,
                    SUM(CASE WHEN level = 'info' THEN 1 ELSE 0 END) as info_count,
                    SUM(CASE WHEN level = 'debug' THEN 1 ELSE 0 END) as debug_count
                 FROM system_logs $whereClause",
                $params
            );
            
            return [
                'success' => true,
                'data' => [
                    'total_logs' => (int)$stats['total_logs'],
                    'by_level' => [
                        'emergency' => (int)$stats['emergency_count'],
                        'alert' => (int)$stats['alert_count'],
                        'critical' => (int)$stats['critical_count'],
                        'error' => (int)$stats['error_count'],
                        'warning' => (int)$stats['warning_count'],
                        'notice' => (int)$stats['notice_count'],
                        'info' => (int)$stats['info_count'],
                        'debug' => (int)$stats['debug_count']
                    ]
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error retrieving log stats'];
        }
    }
    
    public function clearOldLogs($daysToKeep = 30) {
        try {
            $cutoffDate = date('Y-m-d', strtotime("-$daysToKeep days"));
            
            $deleted = $this->db->delete(
                'system_logs',
                'DATE(created_at) < ?',
                [$cutoffDate]
            );
            
            $this->info("Old logs cleared", [
                'days_to_keep' => $daysToKeep,
                'cutoff_date' => $cutoffDate,
                'deleted_count' => $deleted
            ]);
            
            return ['success' => true, 'deleted_count' => $deleted];
            
        } catch (Exception $e) {
            $this->error("Failed to clear old logs", ['error' => $e->getMessage()]);
            return ['success' => false, 'message' => 'Error clearing old logs'];
        }
    }
    
    public function exportLogs($level = null, $dateFrom = null, $dateTo = null, $format = 'csv') {
        try {
            $logs = $this->getLogs($level, 10000, 0, $dateFrom, $dateTo);
            
            if (!$logs['success']) {
                return $logs;
            }
            
            $filename = 'system_logs_' . date('Y-m-d_H-i-s') . '.' . $format;
            
            if ($format === 'csv') {
                return $this->exportToCSV($logs['data'], $filename);
            } elseif ($format === 'json') {
                return $this->exportToJSON($logs['data'], $filename);
            }
            
            return ['success' => false, 'message' => 'Unsupported export format'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error exporting logs'];
        }
    }
    
    private function exportToCSV($logs, $filename) {
        $output = fopen('php://temp', 'w');
        
        // Write CSV headers
        fputcsv($output, ['ID', 'Level', 'Message', 'IP Address', 'User ID', 'Created At']);
        
        // Write data
        foreach ($logs as $log) {
            fputcsv($output, [
                $log['id'],
                $log['level'],
                $log['message'],
                $log['ip_address'],
                $log['user_id'],
                $log['created_at']
            ]);
        }
        
        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);
        
        return [
            'success' => true,
            'filename' => $filename,
            'content' => $csvContent,
            'content_type' => 'text/csv'
        ];
    }
    
    private function exportToJSON($logs, $filename) {
        $jsonContent = json_encode($logs, JSON_PRETTY_PRINT);
        
        return [
            'success' => true,
            'filename' => $filename,
            'content' => $jsonContent,
            'content_type' => 'application/json'
        ];
    }
    
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    public function logPerformanceMetric($metric, $value, $context = []) {
        $this->info("Performance metric: $metric", [
            'metric' => $metric,
            'value' => $value,
            'context' => $context
        ]);
    }
    
    public function logException($exception, $context = []) {
        $this->error("Exception: " . $exception->getMessage(), [
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'context' => $context
        ]);
    }
}
?>
