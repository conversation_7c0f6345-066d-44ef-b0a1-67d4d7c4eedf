<?php

require_once 'Database.php';

class Newsletter {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function subscribe($email, $firstName = '', $lastName = '', $preferences = []) {
        try {
            // Validate email
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return ['success' => false, 'message' => 'Invalid email address'];
            }
            
            // Check if already subscribed
            $existing = $this->db->fetch(
                "SELECT id, status FROM newsletter_subscribers WHERE email = ?",
                [$email]
            );
            
            if ($existing) {
                if ($existing['status'] === 'active') {
                    return ['success' => false, 'message' => 'Email already subscribed'];
                } else {
                    // Reactivate subscription
                    $this->db->update(
                        'newsletter_subscribers',
                        [
                            'status' => 'active',
                            'first_name' => $firstName,
                            'last_name' => $lastName,
                            'preferences' => json_encode($preferences),
                            'updated_at' => date('Y-m-d H:i:s')
                        ],
                        'id = ?',
                        [$existing['id']]
                    );
                    
                    return ['success' => true, 'message' => 'Subscription reactivated successfully'];
                }
            }
            
            // Generate confirmation token
            $confirmationToken = bin2hex(random_bytes(32));
            
            // Add new subscription
            $subscriberId = $this->db->insert('newsletter_subscribers', [
                'email' => $email,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'preferences' => json_encode($preferences),
                'status' => 'pending',
                'confirmation_token' => $confirmationToken
            ]);
            
            // Send confirmation email
            $this->sendConfirmationEmail($email, $confirmationToken, $firstName);
            
            return [
                'success' => true,
                'message' => 'Subscription successful! Please check your email to confirm.',
                'subscriber_id' => $subscriberId
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error subscribing to newsletter'];
        }
    }
    
    public function confirmSubscription($token) {
        try {
            $subscriber = $this->db->fetch(
                "SELECT id, email, first_name FROM newsletter_subscribers WHERE confirmation_token = ? AND status = 'pending'",
                [$token]
            );
            
            if (!$subscriber) {
                return ['success' => false, 'message' => 'Invalid or expired confirmation token'];
            }
            
            // Activate subscription
            $this->db->update(
                'newsletter_subscribers',
                [
                    'status' => 'active',
                    'confirmed_at' => date('Y-m-d H:i:s'),
                    'confirmation_token' => null
                ],
                'id = ?',
                [$subscriber['id']]
            );
            
            // Send welcome email
            $this->sendWelcomeEmail($subscriber['email'], $subscriber['first_name']);
            
            return ['success' => true, 'message' => 'Email confirmed successfully! Welcome to our newsletter.'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error confirming subscription'];
        }
    }
    
    public function unsubscribe($email, $token = null) {
        try {
            $whereClause = "email = ?";
            $params = [$email];
            
            if ($token) {
                $whereClause .= " AND unsubscribe_token = ?";
                $params[] = $token;
            }
            
            $subscriber = $this->db->fetch(
                "SELECT id FROM newsletter_subscribers WHERE $whereClause",
                $params
            );
            
            if (!$subscriber) {
                return ['success' => false, 'message' => 'Subscriber not found'];
            }
            
            // Update status to unsubscribed
            $this->db->update(
                'newsletter_subscribers',
                [
                    'status' => 'unsubscribed',
                    'unsubscribed_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$subscriber['id']]
            );
            
            return ['success' => true, 'message' => 'Successfully unsubscribed from newsletter'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error unsubscribing from newsletter'];
        }
    }
    
    public function updatePreferences($email, $preferences) {
        try {
            $subscriber = $this->db->fetch(
                "SELECT id FROM newsletter_subscribers WHERE email = ? AND status = 'active'",
                [$email]
            );
            
            if (!$subscriber) {
                return ['success' => false, 'message' => 'Active subscription not found'];
            }
            
            $this->db->update(
                'newsletter_subscribers',
                [
                    'preferences' => json_encode($preferences),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$subscriber['id']]
            );
            
            return ['success' => true, 'message' => 'Preferences updated successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error updating preferences'];
        }
    }
    
    public function getSubscribers($status = 'active', $limit = 1000, $offset = 0) {
        try {
            $subscribers = $this->db->fetchAll(
                "SELECT id, email, first_name, last_name, preferences, created_at, confirmed_at
                 FROM newsletter_subscribers 
                 WHERE status = ?
                 ORDER BY created_at DESC
                 LIMIT ? OFFSET ?",
                [$status, $limit, $offset]
            );
            
            return ['success' => true, 'data' => $subscribers];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading subscribers'];
        }
    }
    
    public function getSubscriberStats() {
        try {
            $stats = $this->db->fetch(
                "SELECT 
                    COUNT(*) as total_subscribers,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_subscribers,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_subscribers,
                    SUM(CASE WHEN status = 'unsubscribed' THEN 1 ELSE 0 END) as unsubscribed_count,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_signups,
                    SUM(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as week_signups
                 FROM newsletter_subscribers"
            );
            
            return [
                'success' => true,
                'data' => [
                    'total_subscribers' => (int)$stats['total_subscribers'],
                    'active_subscribers' => (int)$stats['active_subscribers'],
                    'pending_subscribers' => (int)$stats['pending_subscribers'],
                    'unsubscribed_count' => (int)$stats['unsubscribed_count'],
                    'today_signups' => (int)$stats['today_signups'],
                    'week_signups' => (int)$stats['week_signups']
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading subscriber stats'];
        }
    }
    
    public function createCampaign($subject, $content, $targetAudience = 'active') {
        try {
            $campaignId = $this->db->insert('newsletter_campaigns', [
                'subject' => $subject,
                'content' => $content,
                'target_audience' => $targetAudience,
                'status' => 'draft'
            ]);
            
            return [
                'success' => true,
                'message' => 'Campaign created successfully',
                'campaign_id' => $campaignId
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error creating campaign'];
        }
    }
    
    public function sendCampaign($campaignId) {
        try {
            $campaign = $this->db->fetch(
                "SELECT * FROM newsletter_campaigns WHERE id = ? AND status = 'draft'",
                [$campaignId]
            );
            
            if (!$campaign) {
                return ['success' => false, 'message' => 'Campaign not found or already sent'];
            }
            
            // Get subscribers based on target audience
            $subscribers = $this->getSubscribers($campaign['target_audience']);
            
            if (!$subscribers['success'] || empty($subscribers['data'])) {
                return ['success' => false, 'message' => 'No subscribers found for target audience'];
            }
            
            $sentCount = 0;
            $failedCount = 0;
            
            foreach ($subscribers['data'] as $subscriber) {
                $success = $this->sendEmail(
                    $subscriber['email'],
                    $campaign['subject'],
                    $campaign['content'],
                    $subscriber['first_name']
                );
                
                if ($success) {
                    $sentCount++;
                } else {
                    $failedCount++;
                }
                
                // Log the send attempt
                $this->db->insert('newsletter_sends', [
                    'campaign_id' => $campaignId,
                    'subscriber_id' => $subscriber['id'],
                    'status' => $success ? 'sent' : 'failed'
                ]);
            }
            
            // Update campaign status
            $this->db->update(
                'newsletter_campaigns',
                [
                    'status' => 'sent',
                    'sent_at' => date('Y-m-d H:i:s'),
                    'sent_count' => $sentCount,
                    'failed_count' => $failedCount
                ],
                'id = ?',
                [$campaignId]
            );
            
            return [
                'success' => true,
                'message' => "Campaign sent successfully. Sent: $sentCount, Failed: $failedCount",
                'sent_count' => $sentCount,
                'failed_count' => $failedCount
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error sending campaign'];
        }
    }
    
    private function sendConfirmationEmail($email, $token, $firstName = '') {
        $subject = 'Confirm Your Newsletter Subscription - Infinite Shadow';
        $confirmUrl = $_SERVER['HTTP_HOST'] . "/newsletter-confirm.php?token=$token";
        
        $content = "
        <h2>Welcome to Infinite Shadow Newsletter!</h2>
        <p>Hi " . ($firstName ?: 'there') . ",</p>
        <p>Thank you for subscribing to our newsletter! Please confirm your subscription by clicking the link below:</p>
        <p><a href='$confirmUrl' style='background-color: #6a3de8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>Confirm Subscription</a></p>
        <p>If you didn't subscribe to our newsletter, you can safely ignore this email.</p>
        <p>Best regards,<br>The Infinite Shadow Team</p>
        ";
        
        return $this->sendEmail($email, $subject, $content);
    }
    
    private function sendWelcomeEmail($email, $firstName = '') {
        $subject = 'Welcome to Infinite Shadow Newsletter!';
        
        $content = "
        <h2>Welcome to the Infinite Shadow Community!</h2>
        <p>Hi " . ($firstName ?: 'there') . ",</p>
        <p>Your subscription has been confirmed! You're now part of our exclusive community of anime enthusiasts.</p>
        <p>Here's what you can expect from our newsletter:</p>
        <ul>
            <li>🎌 Latest anime merchandise arrivals</li>
            <li>🎯 Exclusive deals and early access to sales</li>
            <li>📰 Anime news and updates</li>
            <li>🎁 Special subscriber-only promotions</li>
        </ul>
        <p><a href='" . $_SERVER['HTTP_HOST'] . "/shop.html' style='background-color: #6a3de8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>Start Shopping</a></p>
        <p>Thank you for joining us!</p>
        <p>Best regards,<br>The Infinite Shadow Team</p>
        ";
        
        return $this->sendEmail($email, $subject, $content);
    }
    
    private function sendEmail($to, $subject, $content, $firstName = '') {
        // Replace placeholders
        $content = str_replace('{first_name}', $firstName, $content);
        $content = str_replace('{email}', $to, $content);
        
        // Add unsubscribe link
        $unsubscribeUrl = $_SERVER['HTTP_HOST'] . "/newsletter-unsubscribe.php?email=" . urlencode($to);
        $content .= "<br><br><small><a href='$unsubscribeUrl'>Unsubscribe</a> from this newsletter.</small>";
        
        // Basic email headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: Infinite Shadow <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // In a production environment, you would use a proper email service
        // For now, we'll just log the email
        error_log("Newsletter Email - To: $to, Subject: $subject");
        
        // Return true for demo purposes
        return true;
        
        // Uncomment for actual email sending:
        // return mail($to, $subject, $content, implode("\r\n", $headers));
    }
    
    public function generateUnsubscribeToken($email) {
        try {
            $token = bin2hex(random_bytes(16));
            
            $this->db->update(
                'newsletter_subscribers',
                ['unsubscribe_token' => $token],
                'email = ?',
                [$email]
            );
            
            return $token;
            
        } catch (Exception $e) {
            return null;
        }
    }
}
?>
