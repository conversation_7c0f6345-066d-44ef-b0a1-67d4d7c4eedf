<?php

require_once 'Database.php';
require_once 'Cart.php';
require_once 'Product.php';

class Order {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function createOrder($orderData) {
        try {
            $this->db->beginTransaction();
            
            $cart = new Cart();
            $cartItems = $cart->getItems();
            $cartTotals = $cart->getTotals();
            
            if (empty($cartItems)) {
                throw new Exception('Cart is empty');
            }
            
            // Validate cart items
            $validation = $cart->validateCart();
            if (!$validation['valid']) {
                throw new Exception('Cart validation failed: ' . implode(', ', $validation['errors']));
            }
            
            // Generate order number
            $orderNumber = Utils::generateOrderNumber();
            
            // Calculate totals
            $subtotal = $cartTotals['subtotal'];
            $taxAmount = $cartTotals['tax_amount'];
            $shippingAmount = $cartTotals['shipping_cost'];
            $discountAmount = 0;
            $couponCode = null;
            
            // Apply coupon if available
            $appliedCoupon = $cart->getAppliedCoupon();
            if ($appliedCoupon) {
                $discountAmount = $appliedCoupon['discount'];
                $couponCode = $appliedCoupon['code'];
            }
            
            $totalAmount = $subtotal + $taxAmount + $shippingAmount - $discountAmount;
            
            // Prepare order data
            $order = [
                'order_number' => $orderNumber,
                'user_id' => $orderData['user_id'] ?? null,
                'status' => 'pending',
                'total_amount' => $totalAmount,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_amount' => $shippingAmount,
                'discount_amount' => $discountAmount,
                'coupon_code' => $couponCode,
                'payment_method' => $orderData['payment_method'] ?? 'pending',
                'payment_status' => 'pending',
                'billing_address' => json_encode($orderData['billing_address'] ?? []),
                'shipping_address' => json_encode($orderData['shipping_address'] ?? []),
                'notes' => $orderData['notes'] ?? ''
            ];
            
            // Insert order
            $orderId = $this->db->insert('orders', $order);
            
            // Insert order items and update stock
            $product = new Product();
            foreach ($cartItems as $item) {
                $orderItem = [
                    'order_id' => $orderId,
                    'product_id' => $item['product_id'],
                    'product_name' => $item['name'],
                    'product_sku' => $item['sku'] ?? '',
                    'quantity' => $item['quantity'],
                    'price' => $item['unit_price'],
                    'total' => $item['total_price']
                ];
                
                $this->db->insert('order_items', $orderItem);
                
                // Update product stock
                $product->updateStock($item['product_id'], $item['quantity'], 'subtract');
            }
            
            // Record coupon usage
            if ($appliedCoupon) {
                $this->db->insert('coupon_usage', [
                    'coupon_id' => $appliedCoupon['coupon_id'],
                    'user_id' => $orderData['user_id'] ?? null,
                    'order_id' => $orderId
                ]);
                
                // Update coupon used count
                $this->db->query(
                    "UPDATE coupons SET used_count = used_count + 1 WHERE id = ?",
                    [$appliedCoupon['coupon_id']]
                );
            }
            
            // Clear cart
            $cart->clearCart();
            $cart->removeCoupon();
            
            $this->db->commit();
            
            return [
                'success' => true,
                'order_id' => $orderId,
                'order_number' => $orderNumber,
                'total_amount' => $totalAmount
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function getOrderById($orderId) {
        $order = $this->db->fetch(
            "SELECT o.*, u.first_name, u.last_name, u.email 
             FROM orders o 
             LEFT JOIN users u ON o.user_id = u.id 
             WHERE o.id = ?",
            [$orderId]
        );
        
        if ($order) {
            // Decode JSON fields
            $order['billing_address'] = json_decode($order['billing_address'], true);
            $order['shipping_address'] = json_decode($order['shipping_address'], true);
            
            // Get order items
            $order['items'] = $this->getOrderItems($orderId);
        }
        
        return $order;
    }
    
    public function getOrderByNumber($orderNumber) {
        $order = $this->db->fetch(
            "SELECT o.*, u.first_name, u.last_name, u.email 
             FROM orders o 
             LEFT JOIN users u ON o.user_id = u.id 
             WHERE o.order_number = ?",
            [$orderNumber]
        );
        
        if ($order) {
            // Decode JSON fields
            $order['billing_address'] = json_decode($order['billing_address'], true);
            $order['shipping_address'] = json_decode($order['shipping_address'], true);
            
            // Get order items
            $order['items'] = $this->getOrderItems($order['id']);
        }
        
        return $order;
    }
    
    public function getOrderItems($orderId) {
        return $this->db->fetchAll(
            "SELECT oi.*, p.slug as product_slug,
                    (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as product_image
             FROM order_items oi
             LEFT JOIN products p ON oi.product_id = p.id
             WHERE oi.order_id = ?
             ORDER BY oi.id",
            [$orderId]
        );
    }
    
    public function getUserOrders($userId, $limit = null, $offset = 0) {
        $sql = "SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC";
        $params = [$userId];
        
        if ($limit) {
            $sql .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getAllOrders($filters = []) {
        $sql = "SELECT o.*, u.first_name, u.last_name, u.email 
                FROM orders o 
                LEFT JOIN users u ON o.user_id = u.id 
                WHERE 1=1";
        
        $params = [];
        
        // Apply filters
        if (!empty($filters['status'])) {
            $sql .= " AND o.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['payment_status'])) {
            $sql .= " AND o.payment_status = ?";
            $params[] = $filters['payment_status'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND DATE(o.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND DATE(o.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (o.order_number LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // Sorting
        $orderBy = " ORDER BY ";
        switch ($filters['sort'] ?? 'newest') {
            case 'oldest':
                $orderBy .= "o.created_at ASC";
                break;
            case 'amount_high':
                $orderBy .= "o.total_amount DESC";
                break;
            case 'amount_low':
                $orderBy .= "o.total_amount ASC";
                break;
            default:
                $orderBy .= "o.created_at DESC";
        }
        
        $sql .= $orderBy;
        
        // Pagination
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT ?";
            $params[] = (int)$filters['limit'];
            
            if (!empty($filters['offset'])) {
                $sql .= " OFFSET ?";
                $params[] = (int)$filters['offset'];
            }
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function updateOrderStatus($orderId, $status) {
        try {
            $validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
            
            if (!in_array($status, $validStatuses)) {
                return ['success' => false, 'message' => 'Invalid status'];
            }
            
            $this->db->update('orders', ['status' => $status], 'id = ?', [$orderId]);
            
            // If order is cancelled or refunded, restore stock
            if (in_array($status, ['cancelled', 'refunded'])) {
                $this->restoreOrderStock($orderId);
            }
            
            return ['success' => true];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function updatePaymentStatus($orderId, $paymentStatus) {
        try {
            $validStatuses = ['pending', 'paid', 'failed', 'refunded'];
            
            if (!in_array($paymentStatus, $validStatuses)) {
                return ['success' => false, 'message' => 'Invalid payment status'];
            }
            
            $this->db->update('orders', ['payment_status' => $paymentStatus], 'id = ?', [$orderId]);
            
            // If payment is successful and order is still pending, update to processing
            if ($paymentStatus === 'paid') {
                $order = $this->db->fetch("SELECT status FROM orders WHERE id = ?", [$orderId]);
                if ($order && $order['status'] === 'pending') {
                    $this->db->update('orders', ['status' => 'processing'], 'id = ?', [$orderId]);
                }
            }
            
            return ['success' => true];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    private function restoreOrderStock($orderId) {
        $items = $this->getOrderItems($orderId);
        $product = new Product();
        
        foreach ($items as $item) {
            if ($item['product_id']) {
                $product->updateStock($item['product_id'], $item['quantity'], 'add');
            }
        }
    }
    
    public function getOrderStats($dateFrom = null, $dateTo = null) {
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if ($dateFrom) {
            $whereClause .= " AND DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $whereClause .= " AND DATE(created_at) <= ?";
            $params[] = $dateTo;
        }
        
        // Total orders and revenue
        $totals = $this->db->fetch(
            "SELECT COUNT(*) as total_orders, 
                    SUM(total_amount) as total_revenue,
                    AVG(total_amount) as average_order_value
             FROM orders $whereClause",
            $params
        );
        
        // Orders by status
        $statusStats = $this->db->fetchAll(
            "SELECT status, COUNT(*) as count 
             FROM orders $whereClause 
             GROUP BY status",
            $params
        );
        
        // Payment status stats
        $paymentStats = $this->db->fetchAll(
            "SELECT payment_status, COUNT(*) as count 
             FROM orders $whereClause 
             GROUP BY payment_status",
            $params
        );
        
        return [
            'totals' => $totals,
            'status_breakdown' => $statusStats,
            'payment_breakdown' => $paymentStats
        ];
    }
    
    public function getRecentOrders($limit = 10) {
        return $this->getAllOrders(['limit' => $limit]);
    }
    
    public function cancelOrder($orderId, $reason = '') {
        try {
            $this->db->beginTransaction();
            
            $order = $this->getOrderById($orderId);
            
            if (!$order) {
                throw new Exception('Order not found');
            }
            
            if (!in_array($order['status'], ['pending', 'processing'])) {
                throw new Exception('Order cannot be cancelled');
            }
            
            // Update order status
            $this->db->update('orders', [
                'status' => 'cancelled',
                'notes' => $order['notes'] . "\nCancellation reason: " . $reason
            ], 'id = ?', [$orderId]);
            
            // Restore stock
            $this->restoreOrderStock($orderId);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'Order cancelled successfully'];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
