<?php

require_once 'Database.php';

class Product {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function getAllProducts($filters = []) {
        $sql = "SELECT p.*, c.name as category_name, 
                       (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.status = 'active'";
        
        $params = [];
        
        // Apply filters
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($filters['featured'])) {
            $sql .= " AND p.featured = 1";
        }
        
        if (!empty($filters['min_price'])) {
            $sql .= " AND (COALESCE(p.sale_price, p.price) >= ?)";
            $params[] = $filters['min_price'];
        }
        
        if (!empty($filters['max_price'])) {
            $sql .= " AND (COALESCE(p.sale_price, p.price) <= ?)";
            $params[] = $filters['max_price'];
        }
        
        // Sorting
        $orderBy = " ORDER BY ";
        switch ($filters['sort'] ?? 'name') {
            case 'price_low':
                $orderBy .= "COALESCE(p.sale_price, p.price) ASC";
                break;
            case 'price_high':
                $orderBy .= "COALESCE(p.sale_price, p.price) DESC";
                break;
            case 'newest':
                $orderBy .= "p.created_at DESC";
                break;
            case 'featured':
                $orderBy .= "p.featured DESC, p.name ASC";
                break;
            default:
                $orderBy .= "p.name ASC";
        }
        
        $sql .= $orderBy;
        
        // Pagination
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT ?";
            $params[] = (int)$filters['limit'];
            
            if (!empty($filters['offset'])) {
                $sql .= " OFFSET ?";
                $params[] = (int)$filters['offset'];
            }
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getProductById($id) {
        $product = $this->db->fetch(
            "SELECT p.*, c.name as category_name 
             FROM products p 
             LEFT JOIN categories c ON p.category_id = c.id 
             WHERE p.id = ? AND p.status = 'active'",
            [$id]
        );
        
        if ($product) {
            // Get product images
            $product['images'] = $this->getProductImages($id);
            
            // Get product attributes
            $product['attributes'] = $this->getProductAttributes($id);
            
            // Get product reviews
            $product['reviews'] = $this->getProductReviews($id);
            $product['average_rating'] = $this->getAverageRating($id);
        }
        
        return $product;
    }
    
    public function getProductBySlug($slug) {
        $product = $this->db->fetch(
            "SELECT p.*, c.name as category_name 
             FROM products p 
             LEFT JOIN categories c ON p.category_id = c.id 
             WHERE p.slug = ? AND p.status = 'active'",
            [$slug]
        );
        
        if ($product) {
            // Get product images
            $product['images'] = $this->getProductImages($product['id']);
            
            // Get product attributes
            $product['attributes'] = $this->getProductAttributes($product['id']);
            
            // Get product reviews
            $product['reviews'] = $this->getProductReviews($product['id']);
            $product['average_rating'] = $this->getAverageRating($product['id']);
        }
        
        return $product;
    }
    
    public function getProductImages($productId) {
        return $this->db->fetchAll(
            "SELECT * FROM product_images WHERE product_id = ? ORDER BY sort_order ASC, is_primary DESC",
            [$productId]
        );
    }
    
    public function getProductAttributes($productId) {
        return $this->db->fetchAll(
            "SELECT * FROM product_attributes WHERE product_id = ?",
            [$productId]
        );
    }
    
    public function getProductReviews($productId, $status = 'approved') {
        return $this->db->fetchAll(
            "SELECT pr.*, u.first_name, u.last_name 
             FROM product_reviews pr 
             JOIN users u ON pr.user_id = u.id 
             WHERE pr.product_id = ? AND pr.status = ? 
             ORDER BY pr.created_at DESC",
            [$productId, $status]
        );
    }
    
    public function getAverageRating($productId) {
        $result = $this->db->fetch(
            "SELECT AVG(rating) as average_rating, COUNT(*) as review_count 
             FROM product_reviews 
             WHERE product_id = ? AND status = 'approved'",
            [$productId]
        );
        
        return [
            'average' => round($result['average_rating'] ?? 0, 1),
            'count' => $result['review_count'] ?? 0
        ];
    }
    
    public function createProduct($data) {
        try {
            $this->db->beginTransaction();
            
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = Utils::generateSlug($data['name']);
            }
            
            // Generate SKU if not provided
            if (empty($data['sku'])) {
                $data['sku'] = Utils::generateSKU();
            }
            
            // Insert product
            $productId = $this->db->insert('products', $data);
            
            $this->db->commit();
            return ['success' => true, 'product_id' => $productId];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function updateProduct($id, $data) {
        try {
            $this->db->beginTransaction();
            
            // Update slug if name changed
            if (!empty($data['name']) && empty($data['slug'])) {
                $data['slug'] = Utils::generateSlug($data['name']);
            }
            
            $this->db->update('products', $data, 'id = ?', [$id]);
            
            $this->db->commit();
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function deleteProduct($id) {
        try {
            $this->db->beginTransaction();
            
            // Delete product images
            $this->db->delete('product_images', 'product_id = ?', [$id]);
            
            // Delete product attributes
            $this->db->delete('product_attributes', 'product_id = ?', [$id]);
            
            // Delete product reviews
            $this->db->delete('product_reviews', 'product_id = ?', [$id]);
            
            // Delete from cart and wishlist
            $this->db->delete('cart', 'product_id = ?', [$id]);
            $this->db->delete('wishlist', 'product_id = ?', [$id]);
            
            // Delete product
            $this->db->delete('products', 'id = ?', [$id]);
            
            $this->db->commit();
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function addProductImage($productId, $imageData) {
        try {
            $imageData['product_id'] = $productId;
            
            // If this is the first image, make it primary
            $existingImages = $this->getProductImages($productId);
            if (empty($existingImages)) {
                $imageData['is_primary'] = true;
            }
            
            $imageId = $this->db->insert('product_images', $imageData);
            return ['success' => true, 'image_id' => $imageId];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function updateProductImage($imageId, $data) {
        try {
            $this->db->update('product_images', $data, 'id = ?', [$imageId]);
            return ['success' => true];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function deleteProductImage($imageId) {
        try {
            // Get image info before deletion
            $image = $this->db->fetch("SELECT * FROM product_images WHERE id = ?", [$imageId]);
            
            if ($image) {
                // Delete file if it exists
                if ($image['image_type'] === 'file' && file_exists($image['image_path'])) {
                    unlink($image['image_path']);
                }
                
                // Delete from database
                $this->db->delete('product_images', 'id = ?', [$imageId]);
                
                // If this was the primary image, make another image primary
                if ($image['is_primary']) {
                    $this->db->query(
                        "UPDATE product_images SET is_primary = 1 WHERE product_id = ? ORDER BY sort_order ASC LIMIT 1",
                        [$image['product_id']]
                    );
                }
            }
            
            return ['success' => true];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function setPrimaryImage($imageId) {
        try {
            $this->db->beginTransaction();
            
            // Get the product ID for this image
            $image = $this->db->fetch("SELECT product_id FROM product_images WHERE id = ?", [$imageId]);
            
            if ($image) {
                // Remove primary flag from all images of this product
                $this->db->update('product_images', ['is_primary' => false], 'product_id = ?', [$image['product_id']]);
                
                // Set this image as primary
                $this->db->update('product_images', ['is_primary' => true], 'id = ?', [$imageId]);
            }
            
            $this->db->commit();
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function updateStock($productId, $quantity, $operation = 'set') {
        try {
            $product = $this->db->fetch("SELECT stock_quantity FROM products WHERE id = ?", [$productId]);
            
            if (!$product) {
                return ['success' => false, 'message' => 'Product not found'];
            }
            
            $newQuantity = $product['stock_quantity'];
            
            switch ($operation) {
                case 'add':
                    $newQuantity += $quantity;
                    break;
                case 'subtract':
                    $newQuantity -= $quantity;
                    break;
                case 'set':
                default:
                    $newQuantity = $quantity;
                    break;
            }
            
            $newQuantity = max(0, $newQuantity); // Ensure non-negative
            
            // Update stock status based on quantity
            $stockStatus = $newQuantity > 0 ? 'in_stock' : 'out_of_stock';
            
            $this->db->update('products', [
                'stock_quantity' => $newQuantity,
                'stock_status' => $stockStatus
            ], 'id = ?', [$productId]);
            
            return ['success' => true, 'new_quantity' => $newQuantity];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function getFeaturedProducts($limit = 8) {
        return $this->getAllProducts(['featured' => true, 'limit' => $limit]);
    }
    
    public function getRelatedProducts($productId, $limit = 4) {
        $product = $this->db->fetch("SELECT category_id FROM products WHERE id = ?", [$productId]);
        
        if (!$product) {
            return [];
        }
        
        return $this->db->fetchAll(
            "SELECT p.*, 
                    (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
             FROM products p 
             WHERE p.category_id = ? AND p.id != ? AND p.status = 'active' 
             ORDER BY p.featured DESC, RAND() 
             LIMIT ?",
            [$product['category_id'], $productId, $limit]
        );
    }
    
    public function searchProducts($query, $limit = 20) {
        return $this->getAllProducts([
            'search' => $query,
            'limit' => $limit
        ]);
    }
}
