<?php

require_once 'Database.php';

class Reviews {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function addReview($userId, $productId, $rating, $comment, $title = '') {
        try {
            // Validate rating
            if ($rating < 1 || $rating > 5) {
                return ['success' => false, 'message' => 'Rating must be between 1 and 5'];
            }
            
            // Check if product exists
            $product = $this->db->fetch(
                "SELECT id FROM products WHERE id = ? AND status = 'active'",
                [$productId]
            );
            
            if (!$product) {
                return ['success' => false, 'message' => 'Product not found'];
            }
            
            // Check if user already reviewed this product
            $existing = $this->db->fetch(
                "SELECT id FROM product_reviews WHERE user_id = ? AND product_id = ?",
                [$userId, $productId]
            );
            
            if ($existing) {
                return ['success' => false, 'message' => 'You have already reviewed this product'];
            }
            
            // Add review
            $reviewId = $this->db->insert('product_reviews', [
                'user_id' => $userId,
                'product_id' => $productId,
                'rating' => $rating,
                'title' => $title,
                'comment' => $comment,
                'status' => 'pending'
            ]);
            
            // Update product rating
            $this->updateProductRating($productId);
            
            return [
                'success' => true,
                'message' => 'Review submitted successfully',
                'review_id' => $reviewId
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error adding review'];
        }
    }
    
    public function updateReview($reviewId, $userId, $rating, $comment, $title = '') {
        try {
            // Validate rating
            if ($rating < 1 || $rating > 5) {
                return ['success' => false, 'message' => 'Rating must be between 1 and 5'];
            }
            
            // Check if review exists and belongs to user
            $review = $this->db->fetch(
                "SELECT id, product_id FROM product_reviews WHERE id = ? AND user_id = ?",
                [$reviewId, $userId]
            );
            
            if (!$review) {
                return ['success' => false, 'message' => 'Review not found'];
            }
            
            // Update review
            $this->db->update(
                'product_reviews',
                [
                    'rating' => $rating,
                    'title' => $title,
                    'comment' => $comment,
                    'status' => 'pending',
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$reviewId]
            );
            
            // Update product rating
            $this->updateProductRating($review['product_id']);
            
            return ['success' => true, 'message' => 'Review updated successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error updating review'];
        }
    }
    
    public function deleteReview($reviewId, $userId) {
        try {
            // Check if review exists and belongs to user
            $review = $this->db->fetch(
                "SELECT id, product_id FROM product_reviews WHERE id = ? AND user_id = ?",
                [$reviewId, $userId]
            );
            
            if (!$review) {
                return ['success' => false, 'message' => 'Review not found'];
            }
            
            // Delete review
            $this->db->delete('product_reviews', 'id = ?', [$reviewId]);
            
            // Update product rating
            $this->updateProductRating($review['product_id']);
            
            return ['success' => true, 'message' => 'Review deleted successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error deleting review'];
        }
    }
    
    public function getProductReviews($productId, $limit = 20, $offset = 0, $status = 'approved') {
        try {
            $reviews = $this->db->fetchAll(
                "SELECT pr.*, u.first_name, u.last_name, u.username
                 FROM product_reviews pr
                 JOIN users u ON pr.user_id = u.id
                 WHERE pr.product_id = ? AND pr.status = ?
                 ORDER BY pr.created_at DESC
                 LIMIT ? OFFSET ?",
                [$productId, $status, $limit, $offset]
            );
            
            return ['success' => true, 'data' => $reviews];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading reviews'];
        }
    }
    
    public function getUserReviews($userId, $limit = 20, $offset = 0) {
        try {
            $reviews = $this->db->fetchAll(
                "SELECT pr.*, p.name as product_name, p.primary_image
                 FROM product_reviews pr
                 JOIN products p ON pr.product_id = p.id
                 WHERE pr.user_id = ?
                 ORDER BY pr.created_at DESC
                 LIMIT ? OFFSET ?",
                [$userId, $limit, $offset]
            );
            
            return ['success' => true, 'data' => $reviews];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading user reviews'];
        }
    }
    
    public function getReviewStats($productId) {
        try {
            $stats = $this->db->fetch(
                "SELECT 
                    COUNT(*) as total_reviews,
                    AVG(rating) as average_rating,
                    SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
                    SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
                    SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
                    SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
                    SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
                 FROM product_reviews
                 WHERE product_id = ? AND status = 'approved'",
                [$productId]
            );
            
            return [
                'success' => true,
                'data' => [
                    'total_reviews' => (int)$stats['total_reviews'],
                    'average_rating' => round((float)$stats['average_rating'], 1),
                    'rating_distribution' => [
                        5 => (int)$stats['five_star'],
                        4 => (int)$stats['four_star'],
                        3 => (int)$stats['three_star'],
                        2 => (int)$stats['two_star'],
                        1 => (int)$stats['one_star']
                    ]
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading review stats'];
        }
    }
    
    public function hasUserReviewed($userId, $productId) {
        try {
            $review = $this->db->fetch(
                "SELECT id FROM product_reviews WHERE user_id = ? AND product_id = ?",
                [$userId, $productId]
            );
            
            return !empty($review);
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function moderateReview($reviewId, $status, $moderatorId) {
        try {
            if (!in_array($status, ['approved', 'rejected', 'pending'])) {
                return ['success' => false, 'message' => 'Invalid status'];
            }
            
            $review = $this->db->fetch(
                "SELECT product_id FROM product_reviews WHERE id = ?",
                [$reviewId]
            );
            
            if (!$review) {
                return ['success' => false, 'message' => 'Review not found'];
            }
            
            $this->db->update(
                'product_reviews',
                [
                    'status' => $status,
                    'moderated_by' => $moderatorId,
                    'moderated_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$reviewId]
            );
            
            // Update product rating if approved/rejected
            if ($status === 'approved' || $status === 'rejected') {
                $this->updateProductRating($review['product_id']);
            }
            
            return ['success' => true, 'message' => 'Review moderated successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error moderating review'];
        }
    }
    
    public function getPendingReviews($limit = 50, $offset = 0) {
        try {
            $reviews = $this->db->fetchAll(
                "SELECT pr.*, u.first_name, u.last_name, u.username, p.name as product_name
                 FROM product_reviews pr
                 JOIN users u ON pr.user_id = u.id
                 JOIN products p ON pr.product_id = p.id
                 WHERE pr.status = 'pending'
                 ORDER BY pr.created_at ASC
                 LIMIT ? OFFSET ?",
                [$limit, $offset]
            );
            
            return ['success' => true, 'data' => $reviews];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading pending reviews'];
        }
    }
    
    public function markReviewHelpful($reviewId, $userId, $isHelpful = true) {
        try {
            // Check if user already marked this review
            $existing = $this->db->fetch(
                "SELECT id, is_helpful FROM review_helpfulness WHERE review_id = ? AND user_id = ?",
                [$reviewId, $userId]
            );
            
            if ($existing) {
                // Update existing vote
                $this->db->update(
                    'review_helpfulness',
                    ['is_helpful' => $isHelpful ? 1 : 0],
                    'id = ?',
                    [$existing['id']]
                );
            } else {
                // Add new vote
                $this->db->insert('review_helpfulness', [
                    'review_id' => $reviewId,
                    'user_id' => $userId,
                    'is_helpful' => $isHelpful ? 1 : 0
                ]);
            }
            
            // Get updated counts
            $counts = $this->db->fetch(
                "SELECT 
                    SUM(CASE WHEN is_helpful = 1 THEN 1 ELSE 0 END) as helpful_count,
                    SUM(CASE WHEN is_helpful = 0 THEN 1 ELSE 0 END) as not_helpful_count
                 FROM review_helpfulness
                 WHERE review_id = ?",
                [$reviewId]
            );
            
            return [
                'success' => true,
                'data' => [
                    'helpful_count' => (int)$counts['helpful_count'],
                    'not_helpful_count' => (int)$counts['not_helpful_count']
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error marking review helpfulness'];
        }
    }
    
    private function updateProductRating($productId) {
        try {
            $stats = $this->db->fetch(
                "SELECT AVG(rating) as avg_rating, COUNT(*) as review_count
                 FROM product_reviews
                 WHERE product_id = ? AND status = 'approved'",
                [$productId]
            );
            
            $this->db->update(
                'products',
                [
                    'average_rating' => $stats['avg_rating'] ? round($stats['avg_rating'], 1) : 0,
                    'review_count' => (int)$stats['review_count']
                ],
                'id = ?',
                [$productId]
            );
            
        } catch (Exception $e) {
            // Log error but don't throw
            error_log('Error updating product rating: ' . $e->getMessage());
        }
    }
    
    public function getTopRatedProducts($limit = 10) {
        try {
            $products = $this->db->fetchAll(
                "SELECT p.*, AVG(pr.rating) as avg_rating, COUNT(pr.id) as review_count
                 FROM products p
                 JOIN product_reviews pr ON p.id = pr.product_id
                 WHERE p.status = 'active' AND pr.status = 'approved'
                 GROUP BY p.id
                 HAVING review_count >= 3
                 ORDER BY avg_rating DESC, review_count DESC
                 LIMIT ?",
                [$limit]
            );
            
            return ['success' => true, 'data' => $products];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading top rated products'];
        }
    }
}
?>
