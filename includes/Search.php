<?php

require_once 'Database.php';

class Search {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function searchProducts($query, $filters = [], $limit = 20, $offset = 0) {
        try {
            $searchTerms = $this->parseSearchQuery($query);
            $whereConditions = ["p.status = 'active'"];
            $params = [];
            
            // Build search conditions
            if (!empty($searchTerms)) {
                $searchConditions = [];
                foreach ($searchTerms as $term) {
                    $searchConditions[] = "(p.name LIKE ? OR p.description LIKE ? OR p.tags LIKE ? OR c.name LIKE ?)";
                    $likeTerm = "%$term%";
                    $params = array_merge($params, [$likeTerm, $likeTerm, $likeTerm, $likeTerm]);
                }
                $whereConditions[] = "(" . implode(" AND ", $searchConditions) . ")";
            }
            
            // Apply filters
            if (!empty($filters['category_id'])) {
                $whereConditions[] = "p.category_id = ?";
                $params[] = $filters['category_id'];
            }
            
            if (!empty($filters['min_price'])) {
                $whereConditions[] = "(CASE WHEN p.sale_price > 0 THEN p.sale_price ELSE p.price END) >= ?";
                $params[] = $filters['min_price'];
            }
            
            if (!empty($filters['max_price'])) {
                $whereConditions[] = "(CASE WHEN p.sale_price > 0 THEN p.sale_price ELSE p.price END) <= ?";
                $params[] = $filters['max_price'];
            }
            
            if (!empty($filters['min_rating'])) {
                $whereConditions[] = "p.average_rating >= ?";
                $params[] = $filters['min_rating'];
            }
            
            if (!empty($filters['in_stock'])) {
                $whereConditions[] = "p.stock_quantity > 0";
            }
            
            if (!empty($filters['on_sale'])) {
                $whereConditions[] = "p.sale_price > 0";
            }
            
            // Build ORDER BY clause
            $orderBy = $this->buildOrderBy($filters['sort'] ?? 'relevance', $searchTerms);
            
            // Build the main query
            $whereClause = implode(" AND ", $whereConditions);
            
            $sql = "SELECT p.*, c.name as category_name,
                           (CASE WHEN p.sale_price > 0 THEN p.sale_price ELSE p.price END) as final_price
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE $whereClause
                    $orderBy
                    LIMIT ? OFFSET ?";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $products = $this->db->fetchAll($sql, $params);
            
            // Get total count for pagination
            $countSql = "SELECT COUNT(*) as total
                         FROM products p
                         LEFT JOIN categories c ON p.category_id = c.id
                         WHERE $whereClause";
            
            $countParams = array_slice($params, 0, -2); // Remove limit and offset
            $totalResult = $this->db->fetch($countSql, $countParams);
            $total = $totalResult['total'];
            
            // Log search query
            $this->logSearch($query, $total);
            
            return [
                'success' => true,
                'data' => [
                    'products' => $products,
                    'total' => (int)$total,
                    'limit' => $limit,
                    'offset' => $offset,
                    'query' => $query,
                    'filters' => $filters
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error performing search'];
        }
    }
    
    public function getSearchSuggestions($query, $limit = 10) {
        try {
            $suggestions = [];
            $likeTerm = "%$query%";
            
            // Product name suggestions
            $productSuggestions = $this->db->fetchAll(
                "SELECT DISTINCT name as suggestion, 'product' as type
                 FROM products 
                 WHERE name LIKE ? AND status = 'active'
                 ORDER BY name
                 LIMIT ?",
                [$likeTerm, $limit]
            );
            
            // Category suggestions
            $categorySuggestions = $this->db->fetchAll(
                "SELECT DISTINCT name as suggestion, 'category' as type
                 FROM categories 
                 WHERE name LIKE ? AND status = 'active'
                 ORDER BY name
                 LIMIT ?",
                [$likeTerm, $limit]
            );
            
            // Tag suggestions
            $tagSuggestions = $this->db->fetchAll(
                "SELECT DISTINCT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) as suggestion, 'tag' as type
                 FROM products
                 CROSS JOIN (SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers
                 WHERE CHAR_LENGTH(tags) - CHAR_LENGTH(REPLACE(tags, ',', '')) >= numbers.n - 1
                 AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) LIKE ?
                 AND status = 'active'
                 ORDER BY suggestion
                 LIMIT ?",
                [$likeTerm, $limit]
            );
            
            $suggestions = array_merge($productSuggestions, $categorySuggestions, $tagSuggestions);
            
            // Remove duplicates and limit results
            $uniqueSuggestions = [];
            $seen = [];
            
            foreach ($suggestions as $suggestion) {
                $key = strtolower($suggestion['suggestion']);
                if (!isset($seen[$key]) && count($uniqueSuggestions) < $limit) {
                    $seen[$key] = true;
                    $uniqueSuggestions[] = $suggestion;
                }
            }
            
            return ['success' => true, 'data' => $uniqueSuggestions];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error getting search suggestions'];
        }
    }
    
    public function getPopularSearches($limit = 10) {
        try {
            $popular = $this->db->fetchAll(
                "SELECT query, COUNT(*) as search_count
                 FROM search_logs
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                 AND result_count > 0
                 GROUP BY query
                 ORDER BY search_count DESC
                 LIMIT ?",
                [$limit]
            );
            
            return ['success' => true, 'data' => $popular];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error getting popular searches'];
        }
    }
    
    public function getSearchHistory($userId, $limit = 20) {
        try {
            $history = $this->db->fetchAll(
                "SELECT DISTINCT query, MAX(created_at) as last_searched
                 FROM search_logs
                 WHERE user_id = ?
                 GROUP BY query
                 ORDER BY last_searched DESC
                 LIMIT ?",
                [$userId, $limit]
            );
            
            return ['success' => true, 'data' => $history];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error getting search history'];
        }
    }
    
    public function getSearchStats($dateFrom = null, $dateTo = null) {
        try {
            $whereClause = "WHERE 1=1";
            $params = [];
            
            if ($dateFrom) {
                $whereClause .= " AND DATE(created_at) >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $whereClause .= " AND DATE(created_at) <= ?";
                $params[] = $dateTo;
            }
            
            $stats = $this->db->fetch(
                "SELECT 
                    COUNT(*) as total_searches,
                    COUNT(DISTINCT query) as unique_queries,
                    COUNT(DISTINCT user_id) as unique_users,
                    AVG(result_count) as avg_results,
                    SUM(CASE WHEN result_count = 0 THEN 1 ELSE 0 END) as no_result_searches
                 FROM search_logs $whereClause",
                $params
            );
            
            // Top searches
            $topSearches = $this->db->fetchAll(
                "SELECT query, COUNT(*) as count
                 FROM search_logs $whereClause
                 GROUP BY query
                 ORDER BY count DESC
                 LIMIT 10",
                $params
            );
            
            // No result searches
            $noResultSearches = $this->db->fetchAll(
                "SELECT query, COUNT(*) as count
                 FROM search_logs $whereClause
                 AND result_count = 0
                 GROUP BY query
                 ORDER BY count DESC
                 LIMIT 10",
                $params
            );
            
            return [
                'success' => true,
                'data' => [
                    'overview' => [
                        'total_searches' => (int)$stats['total_searches'],
                        'unique_queries' => (int)$stats['unique_queries'],
                        'unique_users' => (int)$stats['unique_users'],
                        'average_results' => round((float)$stats['avg_results'], 1),
                        'no_result_searches' => (int)$stats['no_result_searches']
                    ],
                    'top_searches' => $topSearches,
                    'no_result_searches' => $noResultSearches
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error getting search stats'];
        }
    }
    
    private function parseSearchQuery($query) {
        // Clean and split the search query
        $query = trim($query);
        if (empty($query)) {
            return [];
        }
        
        // Remove special characters and split by spaces
        $terms = preg_split('/\s+/', $query);
        
        // Filter out very short terms and duplicates
        $terms = array_filter($terms, function($term) {
            return strlen($term) >= 2;
        });
        
        return array_unique($terms);
    }
    
    private function buildOrderBy($sort, $searchTerms = []) {
        switch ($sort) {
            case 'price_low':
                return "ORDER BY final_price ASC";
            case 'price_high':
                return "ORDER BY final_price DESC";
            case 'newest':
                return "ORDER BY p.created_at DESC";
            case 'rating':
                return "ORDER BY p.average_rating DESC, p.review_count DESC";
            case 'popular':
                return "ORDER BY p.view_count DESC";
            case 'name':
                return "ORDER BY p.name ASC";
            case 'relevance':
            default:
                if (!empty($searchTerms)) {
                    // Build relevance score based on search terms
                    $relevanceScore = "(" .
                        "CASE WHEN p.name LIKE '%" . implode("%' THEN 10 ELSE 0 END + CASE WHEN p.name LIKE '%", $searchTerms) . "%' THEN 10 ELSE 0 END + " .
                        "CASE WHEN p.description LIKE '%" . implode("%' THEN 5 ELSE 0 END + CASE WHEN p.description LIKE '%", $searchTerms) . "%' THEN 5 ELSE 0 END + " .
                        "CASE WHEN p.tags LIKE '%" . implode("%' THEN 3 ELSE 0 END + CASE WHEN p.tags LIKE '%", $searchTerms) . "%' THEN 3 ELSE 0 END" .
                        ")";
                    return "ORDER BY $relevanceScore DESC, p.average_rating DESC";
                } else {
                    return "ORDER BY p.created_at DESC";
                }
        }
    }
    
    private function logSearch($query, $resultCount, $userId = null) {
        try {
            $this->db->insert('search_logs', [
                'query' => $query,
                'result_count' => $resultCount,
                'user_id' => $userId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (Exception $e) {
            // Log error but don't fail the search
            error_log('Error logging search: ' . $e->getMessage());
        }
    }
    
    public function clearSearchHistory($userId) {
        try {
            $this->db->delete('search_logs', 'user_id = ?', [$userId]);
            return ['success' => true, 'message' => 'Search history cleared'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error clearing search history'];
        }
    }
    
    public function getRelatedProducts($productId, $limit = 6) {
        try {
            // Get the current product's details
            $product = $this->db->fetch(
                "SELECT category_id, tags FROM products WHERE id = ?",
                [$productId]
            );
            
            if (!$product) {
                return ['success' => false, 'message' => 'Product not found'];
            }
            
            // Find related products based on category and tags
            $related = $this->db->fetchAll(
                "SELECT p.*, c.name as category_name,
                        (CASE WHEN p.sale_price > 0 THEN p.sale_price ELSE p.price END) as final_price
                 FROM products p
                 LEFT JOIN categories c ON p.category_id = c.id
                 WHERE p.id != ? 
                 AND p.status = 'active'
                 AND (p.category_id = ? OR p.tags LIKE ?)
                 ORDER BY 
                    CASE WHEN p.category_id = ? THEN 2 ELSE 0 END +
                    CASE WHEN p.tags LIKE ? THEN 1 ELSE 0 END DESC,
                    p.average_rating DESC
                 LIMIT ?",
                [
                    $productId,
                    $product['category_id'],
                    '%' . $product['tags'] . '%',
                    $product['category_id'],
                    '%' . $product['tags'] . '%',
                    $limit
                ]
            );
            
            return ['success' => true, 'data' => $related];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error getting related products'];
        }
    }
}
?>
