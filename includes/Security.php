<?php

class Security {
    
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    public static function validatePassword($password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/', $password);
    }
    
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = self::generateToken();
        }
        return $_SESSION['csrf_token'];
    }
    
    public static function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    public static function rateLimitCheck($identifier, $maxAttempts = 5, $timeWindow = 300) {
        $key = "rate_limit_" . md5($identifier);
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
        }
        
        $data = $_SESSION[$key];
        
        // Reset if time window has passed
        if (time() - $data['first_attempt'] > $timeWindow) {
            $_SESSION[$key] = ['count' => 1, 'first_attempt' => time()];
            return true;
        }
        
        // Check if limit exceeded
        if ($data['count'] >= $maxAttempts) {
            return false;
        }
        
        // Increment counter
        $_SESSION[$key]['count']++;
        return true;
    }
    
    public static function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880) {
        $errors = [];
        
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $errors[] = 'No file uploaded or upload error';
            return ['valid' => false, 'errors' => $errors];
        }
        
        // Check file size
        if ($file['size'] > $maxSize) {
            $errors[] = 'File size exceeds maximum allowed size';
        }
        
        // Check file type
        if (!empty($allowedTypes)) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);
            
            if (!in_array($mimeType, $allowedTypes)) {
                $errors[] = 'File type not allowed';
            }
        }
        
        // Check for malicious content
        $content = file_get_contents($file['tmp_name']);
        if (strpos($content, '<?php') !== false || strpos($content, '<script') !== false) {
            $errors[] = 'File contains potentially malicious content';
        }
        
        return ['valid' => empty($errors), 'errors' => $errors];
    }
    
    public static function preventSQLInjection($input) {
        // This should be used with prepared statements, not as a replacement
        return addslashes($input);
    }
    
    public static function preventXSS($input) {
        return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
    
    public static function validateURL($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    public static function isValidIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    public static function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (self::isValidIP($ip)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    public static function logSecurityEvent($event, $details = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => self::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'details' => $details
        ];
        
        error_log('SECURITY: ' . json_encode($logData));
    }
    
    public static function checkPasswordStrength($password) {
        $score = 0;
        $feedback = [];
        
        // Length check
        if (strlen($password) >= 8) {
            $score += 1;
        } else {
            $feedback[] = 'Password should be at least 8 characters long';
        }
        
        // Uppercase check
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password should contain at least one uppercase letter';
        }
        
        // Lowercase check
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password should contain at least one lowercase letter';
        }
        
        // Number check
        if (preg_match('/\d/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password should contain at least one number';
        }
        
        // Special character check
        if (preg_match('/[^a-zA-Z\d]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password should contain at least one special character';
        }
        
        // Determine strength
        $strength = 'weak';
        if ($score >= 4) {
            $strength = 'strong';
        } elseif ($score >= 3) {
            $strength = 'medium';
        }
        
        return [
            'score' => $score,
            'strength' => $strength,
            'feedback' => $feedback
        ];
    }
    
    public static function encryptData($data, $key) {
        $cipher = 'AES-256-CBC';
        $iv = random_bytes(openssl_cipher_iv_length($cipher));
        $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    public static function decryptData($encryptedData, $key) {
        $cipher = 'AES-256-CBC';
        $data = base64_decode($encryptedData);
        $ivLength = openssl_cipher_iv_length($cipher);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);
        return openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
    }
    
    public static function generateSecureFilename($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // Sanitize filename
        $filename = preg_replace('/[^a-zA-Z0-9_-]/', '', $filename);
        $filename = substr($filename, 0, 50); // Limit length
        
        // Add timestamp and random string
        $timestamp = time();
        $random = bin2hex(random_bytes(8));
        
        return $filename . '_' . $timestamp . '_' . $random . '.' . $extension;
    }
    
    public static function validateImageFile($file) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 5 * 1024 * 1024; // 5MB
        
        $validation = self::validateFileUpload($file, $allowedTypes, $maxSize);
        
        if (!$validation['valid']) {
            return $validation;
        }
        
        // Additional image-specific checks
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return ['valid' => false, 'errors' => ['File is not a valid image']];
        }
        
        // Check image dimensions
        $maxWidth = 2000;
        $maxHeight = 2000;
        
        if ($imageInfo[0] > $maxWidth || $imageInfo[1] > $maxHeight) {
            return ['valid' => false, 'errors' => ['Image dimensions too large']];
        }
        
        return ['valid' => true, 'errors' => []];
    }
    
    public static function sanitizeFilename($filename) {
        // Remove path traversal attempts
        $filename = basename($filename);
        
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Prevent hidden files
        $filename = ltrim($filename, '.');
        
        // Ensure filename is not empty
        if (empty($filename)) {
            $filename = 'file_' . time();
        }
        
        return $filename;
    }
    
    public static function checkBruteForce($identifier, $maxAttempts = 5, $lockoutTime = 900) {
        $key = "brute_force_" . md5($identifier);
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['attempts' => 0, 'locked_until' => 0];
        }
        
        $data = $_SESSION[$key];
        
        // Check if currently locked out
        if ($data['locked_until'] > time()) {
            return [
                'allowed' => false,
                'locked_until' => $data['locked_until'],
                'remaining_time' => $data['locked_until'] - time()
            ];
        }
        
        // Reset if lockout period has passed
        if ($data['locked_until'] > 0 && $data['locked_until'] <= time()) {
            $_SESSION[$key] = ['attempts' => 0, 'locked_until' => 0];
            $data = $_SESSION[$key];
        }
        
        return [
            'allowed' => true,
            'attempts' => $data['attempts'],
            'remaining_attempts' => max(0, $maxAttempts - $data['attempts'])
        ];
    }
    
    public static function recordFailedAttempt($identifier, $maxAttempts = 5, $lockoutTime = 900) {
        $key = "brute_force_" . md5($identifier);
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['attempts' => 0, 'locked_until' => 0];
        }
        
        $_SESSION[$key]['attempts']++;
        
        if ($_SESSION[$key]['attempts'] >= $maxAttempts) {
            $_SESSION[$key]['locked_until'] = time() + $lockoutTime;
            self::logSecurityEvent('brute_force_lockout', [
                'identifier' => $identifier,
                'attempts' => $_SESSION[$key]['attempts']
            ]);
        }
        
        return $_SESSION[$key];
    }
    
    public static function resetFailedAttempts($identifier) {
        $key = "brute_force_" . md5($identifier);
        unset($_SESSION[$key]);
    }
}
?>
