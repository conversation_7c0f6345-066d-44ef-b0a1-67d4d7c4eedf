<?php

require_once 'Database.php';

class Wishlist {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function addToWishlist($userId, $productId) {
        try {
            // Check if product exists
            $product = $this->db->fetch(
                "SELECT id FROM products WHERE id = ? AND status = 'active'",
                [$productId]
            );
            
            if (!$product) {
                return ['success' => false, 'message' => 'Product not found'];
            }
            
            // Check if already in wishlist
            $existing = $this->db->fetch(
                "SELECT id FROM wishlist WHERE user_id = ? AND product_id = ?",
                [$userId, $productId]
            );
            
            if ($existing) {
                return ['success' => false, 'message' => 'Product already in wishlist'];
            }
            
            // Add to wishlist
            $this->db->insert('wishlist', [
                'user_id' => $userId,
                'product_id' => $productId
            ]);
            
            return ['success' => true, 'message' => 'Product added to wishlist'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error adding to wishlist'];
        }
    }
    
    public function removeFromWishlist($userId, $productId) {
        try {
            $deleted = $this->db->delete(
                'wishlist',
                'user_id = ? AND product_id = ?',
                [$userId, $productId]
            );
            
            if ($deleted) {
                return ['success' => true, 'message' => 'Product removed from wishlist'];
            } else {
                return ['success' => false, 'message' => 'Product not found in wishlist'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error removing from wishlist'];
        }
    }
    
    public function getUserWishlist($userId, $limit = 50, $offset = 0) {
        try {
            $wishlist = $this->db->fetchAll(
                "SELECT w.*, p.name, p.price, p.sale_price, p.primary_image, p.slug,
                        c.name as category_name
                 FROM wishlist w
                 JOIN products p ON w.product_id = p.id
                 LEFT JOIN categories c ON p.category_id = c.id
                 WHERE w.user_id = ? AND p.status = 'active'
                 ORDER BY w.created_at DESC
                 LIMIT ? OFFSET ?",
                [$userId, $limit, $offset]
            );
            
            return ['success' => true, 'data' => $wishlist];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading wishlist'];
        }
    }
    
    public function getWishlistCount($userId) {
        try {
            $count = $this->db->fetch(
                "SELECT COUNT(*) as count FROM wishlist w
                 JOIN products p ON w.product_id = p.id
                 WHERE w.user_id = ? AND p.status = 'active'",
                [$userId]
            );
            
            return (int)$count['count'];
            
        } catch (Exception $e) {
            return 0;
        }
    }
    
    public function isInWishlist($userId, $productId) {
        try {
            $item = $this->db->fetch(
                "SELECT id FROM wishlist WHERE user_id = ? AND product_id = ?",
                [$userId, $productId]
            );
            
            return !empty($item);
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function clearWishlist($userId) {
        try {
            $this->db->delete('wishlist', 'user_id = ?', [$userId]);
            return ['success' => true, 'message' => 'Wishlist cleared'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error clearing wishlist'];
        }
    }
    
    public function moveToCart($userId, $productId, $quantity = 1) {
        try {
            $this->db->beginTransaction();
            
            // Check if product is in wishlist
            $wishlistItem = $this->db->fetch(
                "SELECT id FROM wishlist WHERE user_id = ? AND product_id = ?",
                [$userId, $productId]
            );
            
            if (!$wishlistItem) {
                throw new Exception('Product not found in wishlist');
            }
            
            // Add to cart (assuming Cart class exists)
            require_once 'Cart.php';
            $cart = new Cart();
            $cartResult = $cart->addToCart($userId, $productId, $quantity);
            
            if (!$cartResult['success']) {
                throw new Exception($cartResult['message']);
            }
            
            // Remove from wishlist
            $this->db->delete(
                'wishlist',
                'user_id = ? AND product_id = ?',
                [$userId, $productId]
            );
            
            $this->db->commit();
            return ['success' => true, 'message' => 'Product moved to cart'];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function getWishlistStats($userId) {
        try {
            $stats = $this->db->fetch(
                "SELECT 
                    COUNT(*) as total_items,
                    COALESCE(SUM(CASE WHEN p.sale_price > 0 THEN p.sale_price ELSE p.price END), 0) as total_value,
                    COALESCE(AVG(CASE WHEN p.sale_price > 0 THEN p.sale_price ELSE p.price END), 0) as avg_price
                 FROM wishlist w
                 JOIN products p ON w.product_id = p.id
                 WHERE w.user_id = ? AND p.status = 'active'",
                [$userId]
            );
            
            return [
                'success' => true,
                'data' => [
                    'total_items' => (int)$stats['total_items'],
                    'total_value' => (float)$stats['total_value'],
                    'average_price' => (float)$stats['avg_price']
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading wishlist stats'];
        }
    }
    
    public function getPopularWishlistItems($limit = 10) {
        try {
            $popular = $this->db->fetchAll(
                "SELECT p.id, p.name, p.price, p.sale_price, p.primary_image, p.slug,
                        COUNT(w.id) as wishlist_count
                 FROM products p
                 JOIN wishlist w ON p.id = w.product_id
                 WHERE p.status = 'active'
                 GROUP BY p.id
                 ORDER BY wishlist_count DESC
                 LIMIT ?",
                [$limit]
            );
            
            return ['success' => true, 'data' => $popular];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading popular items'];
        }
    }
    
    public function shareWishlist($userId, $isPublic = false) {
        try {
            // Generate a unique share token
            $shareToken = bin2hex(random_bytes(16));
            
            // Update or create wishlist share settings
            $existing = $this->db->fetch(
                "SELECT id FROM wishlist_shares WHERE user_id = ?",
                [$userId]
            );
            
            if ($existing) {
                $this->db->update(
                    'wishlist_shares',
                    [
                        'share_token' => $shareToken,
                        'is_public' => $isPublic ? 1 : 0,
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    'user_id = ?',
                    [$userId]
                );
            } else {
                $this->db->insert('wishlist_shares', [
                    'user_id' => $userId,
                    'share_token' => $shareToken,
                    'is_public' => $isPublic ? 1 : 0
                ]);
            }
            
            $shareUrl = $_SERVER['HTTP_HOST'] . '/wishlist-shared.php?token=' . $shareToken;
            
            return [
                'success' => true,
                'data' => [
                    'share_token' => $shareToken,
                    'share_url' => $shareUrl,
                    'is_public' => $isPublic
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error creating share link'];
        }
    }
    
    public function getSharedWishlist($shareToken) {
        try {
            $share = $this->db->fetch(
                "SELECT ws.*, u.first_name, u.last_name
                 FROM wishlist_shares ws
                 JOIN users u ON ws.user_id = u.id
                 WHERE ws.share_token = ? AND ws.is_public = 1",
                [$shareToken]
            );
            
            if (!$share) {
                return ['success' => false, 'message' => 'Wishlist not found or not public'];
            }
            
            $wishlist = $this->getUserWishlist($share['user_id']);
            
            return [
                'success' => true,
                'data' => [
                    'owner' => $share['first_name'] . ' ' . $share['last_name'],
                    'wishlist' => $wishlist['data']
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error loading shared wishlist'];
        }
    }
}
?>
