// Admin Analytics JavaScript

class AdminAnalytics {
    constructor() {
        this.charts = {};
        this.currentFilters = {
            dateRange: 30,
            category: '',
            startDate: null,
            endDate: null
        };
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadCategories();
        this.loadAnalytics();
    }

    bindEvents() {
        // Filter events
        document.getElementById('dateRange').addEventListener('change', (e) => this.handleDateRangeChange(e));
        document.getElementById('applyFilters').addEventListener('click', () => this.applyFilters());
        document.getElementById('refreshBtn').addEventListener('click', () => this.loadAnalytics());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportReport());
    }

    handleDateRangeChange(e) {
        const value = e.target.value;
        const customGroups = document.querySelectorAll('#customDateGroup, #customDateGroup2');
        
        if (value === 'custom') {
            customGroups.forEach(group => group.style.display = 'flex');
        } else {
            customGroups.forEach(group => group.style.display = 'none');
            this.currentFilters.dateRange = parseInt(value);
        }
    }

    async loadCategories() {
        try {
            const response = await fetch('../api/categories.php');
            const data = await response.json();
            
            if (data.success) {
                const categorySelect = document.getElementById('categoryFilter');
                data.data.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    applyFilters() {
        const dateRange = document.getElementById('dateRange').value;
        const category = document.getElementById('categoryFilter').value;
        
        if (dateRange === 'custom') {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!startDate || !endDate) {
                alert('Please select both start and end dates for custom range');
                return;
            }
            
            this.currentFilters.startDate = startDate;
            this.currentFilters.endDate = endDate;
            this.currentFilters.dateRange = null;
        } else {
            this.currentFilters.dateRange = parseInt(dateRange);
            this.currentFilters.startDate = null;
            this.currentFilters.endDate = null;
        }
        
        this.currentFilters.category = category;
        this.loadAnalytics();
    }

    async loadAnalytics() {
        this.showLoading();
        
        try {
            const params = new URLSearchParams();
            
            if (this.currentFilters.dateRange) {
                params.append('days', this.currentFilters.dateRange);
            } else if (this.currentFilters.startDate && this.currentFilters.endDate) {
                params.append('start_date', this.currentFilters.startDate);
                params.append('end_date', this.currentFilters.endDate);
            }
            
            if (this.currentFilters.category) {
                params.append('category_id', this.currentFilters.category);
            }
            
            const response = await fetch(`../api/analytics.php?${params.toString()}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayAnalytics(data.data);
            } else {
                this.showError('Failed to load analytics data');
            }
        } catch (error) {
            console.error('Error loading analytics:', error);
            this.showError('Error loading analytics data');
        } finally {
            this.hideLoading();
        }
    }

    displayAnalytics(data) {
        this.updateStats(data.stats);
        this.updateCharts(data.charts);
        this.showContent();
    }

    updateStats(stats) {
        // Update revenue
        document.getElementById('totalRevenue').textContent = this.formatCurrency(stats.revenue.current);
        this.updateStatChange('revenueChange', stats.revenue.change);
        
        // Update orders
        document.getElementById('totalOrders').textContent = stats.orders.current.toLocaleString();
        this.updateStatChange('ordersChange', stats.orders.change);
        
        // Update customers
        document.getElementById('newCustomers').textContent = stats.customers.current.toLocaleString();
        this.updateStatChange('customersChange', stats.customers.change);
        
        // Update products sold
        document.getElementById('productsSold').textContent = stats.products_sold.current.toLocaleString();
        this.updateStatChange('productsChange', stats.products_sold.change);
    }

    updateStatChange(elementId, change) {
        const element = document.getElementById(elementId);
        const icon = element.querySelector('i');
        const span = element.querySelector('span');
        
        const isPositive = change >= 0;
        element.className = `stat-change ${isPositive ? 'positive' : 'negative'}`;
        icon.className = `fas fa-arrow-${isPositive ? 'up' : 'down'}`;
        span.textContent = `${Math.abs(change).toFixed(1)}% from last period`;
    }

    updateCharts(chartData) {
        this.createRevenueChart(chartData.revenue_over_time);
        this.createCategoryChart(chartData.sales_by_category);
    }

    createRevenueChart(data) {
        const ctx = document.getElementById('revenueChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts.revenue) {
            this.charts.revenue.destroy();
        }
        
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Revenue',
                    data: data.values,
                    borderColor: 'rgb(106, 61, 232)',
                    backgroundColor: 'rgba(106, 61, 232, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                }
            }
        });
    }

    createCategoryChart(data) {
        const ctx = document.getElementById('categoryChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts.category) {
            this.charts.category.destroy();
        }
        
        const colors = [
            'rgb(106, 61, 232)',
            'rgb(255, 107, 107)',
            'rgb(255, 190, 11)',
            'rgb(72, 199, 116)',
            'rgb(255, 159, 64)'
        ];
        
        this.charts.category = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: colors.slice(0, data.labels.length),
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    showLoading() {
        document.getElementById('analyticsLoading').style.display = 'block';
        document.getElementById('statsGrid').style.display = 'none';
        document.getElementById('chartsGrid').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('analyticsLoading').style.display = 'none';
    }

    showContent() {
        document.getElementById('statsGrid').style.display = 'grid';
        document.getElementById('chartsGrid').style.display = 'grid';
    }

    showError(message) {
        alert('Error: ' + message);
    }

    async exportReport() {
        try {
            const params = new URLSearchParams();
            params.append('export', 'true');
            
            if (this.currentFilters.dateRange) {
                params.append('days', this.currentFilters.dateRange);
            } else if (this.currentFilters.startDate && this.currentFilters.endDate) {
                params.append('start_date', this.currentFilters.startDate);
                params.append('end_date', this.currentFilters.endDate);
            }
            
            if (this.currentFilters.category) {
                params.append('category_id', this.currentFilters.category);
            }
            
            // Create a temporary link to download the report
            const link = document.createElement('a');
            link.href = `../api/analytics.php?${params.toString()}`;
            link.download = `analytics-report-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
        } catch (error) {
            console.error('Error exporting report:', error);
            this.showError('Failed to export report');
        }
    }
}

// Initialize analytics when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new AdminAnalytics();
});
