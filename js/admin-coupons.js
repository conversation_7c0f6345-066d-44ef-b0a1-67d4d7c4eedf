// Admin Coupons Management JavaScript

class CouponManager {
    constructor() {
        this.currentCoupon = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadCoupons();
    }

    bindEvents() {
        // Modal events
        document.getElementById('addCouponBtn').addEventListener('click', () => this.openModal());
        document.getElementById('closeModal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        
        // Form submission
        document.getElementById('couponForm').addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Generate code button
        document.getElementById('generateCodeBtn').addEventListener('click', () => this.generateCouponCode());
        
        // Type change event to update value hint
        document.getElementById('couponType').addEventListener('change', (e) => this.updateValueHint(e.target.value));
        
        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('couponModal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    async loadCoupons() {
        const loading = document.getElementById('couponsLoading');
        const table = document.getElementById('couponsTable');
        
        loading.style.display = 'block';
        table.style.display = 'none';
        
        try {
            const response = await fetch('../api/coupons.php');
            const data = await response.json();
            
            if (data.success) {
                this.displayCoupons(data.data);
            } else {
                this.showError('Failed to load coupons');
            }
        } catch (error) {
            console.error('Error loading coupons:', error);
            this.showError('Error loading coupons');
        } finally {
            loading.style.display = 'none';
            table.style.display = 'table';
        }
    }

    displayCoupons(coupons) {
        const tbody = document.getElementById('couponsTableBody');
        tbody.innerHTML = '';
        
        if (coupons.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 40px; opacity: 0.7;">No coupons found</td></tr>';
            return;
        }
        
        coupons.forEach(coupon => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <span class="coupon-code">${coupon.code}</span>
                </td>
                <td>
                    <span class="coupon-type ${coupon.type}">${coupon.type}</span>
                </td>
                <td>
                    ${coupon.type === 'percentage' ? coupon.value + '%' : '$' + parseFloat(coupon.value).toFixed(2)}
                    ${coupon.minimum_amount > 0 ? '<br><small>Min: $' + parseFloat(coupon.minimum_amount).toFixed(2) + '</small>' : ''}
                </td>
                <td>
                    <div>${coupon.usage_count_actual || 0}${coupon.usage_limit ? ' / ' + coupon.usage_limit : ' / ∞'}</div>
                    ${coupon.usage_limit ? `
                        <div class="usage-bar">
                            <div class="usage-fill" style="width: ${coupon.usage_percentage || 0}%"></div>
                        </div>
                    ` : ''}
                </td>
                <td>
                    ${this.formatDateRange(coupon.start_date, coupon.end_date)}
                </td>
                <td>
                    <span class="coupon-status ${this.getCouponStatusClass(coupon)}">${this.getCouponStatusText(coupon)}</span>
                </td>
                <td>
                    <div class="coupon-actions">
                        <button onclick="couponManager.editCoupon(${coupon.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="couponManager.viewUsage(${coupon.id})" title="View Usage">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                        <button onclick="couponManager.deleteCoupon(${coupon.id})" class="delete-btn" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    formatDateRange(startDate, endDate) {
        const formatDate = (date) => {
            if (!date) return null;
            return new Date(date).toLocaleDateString();
        };
        
        const start = formatDate(startDate);
        const end = formatDate(endDate);
        
        if (start && end) {
            return `${start} - ${end}`;
        } else if (start) {
            return `From ${start}`;
        } else if (end) {
            return `Until ${end}`;
        } else {
            return 'No expiry';
        }
    }

    getCouponStatusClass(coupon) {
        if (coupon.is_expired) return 'expired';
        if (coupon.is_active) return 'active';
        return 'inactive';
    }

    getCouponStatusText(coupon) {
        if (coupon.is_expired) return 'Expired';
        if (coupon.is_active) return 'Active';
        return 'Inactive';
    }

    openModal(coupon = null) {
        this.currentCoupon = coupon;
        const modal = document.getElementById('couponModal');
        const title = document.getElementById('modalTitle');
        const form = document.getElementById('couponForm');
        
        if (coupon) {
            title.textContent = 'Edit Coupon';
            this.populateForm(coupon);
        } else {
            title.textContent = 'Add Coupon';
            form.reset();
            this.updateValueHint('');
        }
        
        modal.style.display = 'block';
    }

    closeModal() {
        const modal = document.getElementById('couponModal');
        modal.style.display = 'none';
        this.currentCoupon = null;
    }

    populateForm(coupon) {
        const form = document.getElementById('couponForm');
        
        Object.keys(coupon).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = coupon[key] == 1;
                } else {
                    input.value = coupon[key] || '';
                }
            }
        });
        
        this.updateValueHint(coupon.type);
    }

    updateValueHint(type) {
        const hint = document.getElementById('valueHint');
        
        switch (type) {
            case 'fixed':
                hint.textContent = 'Enter dollar amount (e.g., 10.00)';
                break;
            case 'percentage':
                hint.textContent = 'Enter percentage (e.g., 15 for 15%)';
                break;
            default:
                hint.textContent = '';
        }
    }

    async generateCouponCode() {
        try {
            const response = await fetch('../api/coupons.php?action=generate_code&length=8');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('couponCode').value = data.code;
            } else {
                this.showError('Failed to generate coupon code');
            }
        } catch (error) {
            console.error('Error generating coupon code:', error);
            this.showError('Error generating coupon code');
        }
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const couponData = {};
        
        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            if (value !== '') {
                couponData[key] = value;
            }
        }
        
        // Validate form
        if (!this.validateForm(couponData)) {
            return;
        }
        
        try {
            const url = this.currentCoupon ? 
                `../api/coupons.php?id=${this.currentCoupon.id}` : 
                '../api/coupons.php';
            
            const method = this.currentCoupon ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(couponData)
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(this.currentCoupon ? 'Coupon updated successfully' : 'Coupon created successfully');
                this.closeModal();
                this.loadCoupons();
            } else {
                this.showError(data.message || 'Failed to save coupon');
            }
        } catch (error) {
            console.error('Error saving coupon:', error);
            this.showError('Error saving coupon');
        }
    }

    validateForm(data) {
        // Check required fields
        if (!data.code || !data.type || !data.value) {
            this.showError('Please fill in all required fields');
            return false;
        }
        
        // Validate percentage value
        if (data.type === 'percentage' && parseFloat(data.value) > 100) {
            this.showError('Percentage value cannot exceed 100%');
            return false;
        }
        
        // Validate dates
        if (data.start_date && data.end_date) {
            if (new Date(data.start_date) > new Date(data.end_date)) {
                this.showError('End date must be after start date');
                return false;
            }
        }
        
        return true;
    }

    async editCoupon(couponId) {
        try {
            const response = await fetch(`../api/coupons.php?id=${couponId}`);
            const data = await response.json();
            
            if (data.success) {
                this.openModal(data.data);
            } else {
                this.showError('Failed to load coupon details');
            }
        } catch (error) {
            console.error('Error loading coupon:', error);
            this.showError('Error loading coupon');
        }
    }

    async deleteCoupon(couponId) {
        if (!confirm('Are you sure you want to delete this coupon? This action cannot be undone.')) {
            return;
        }
        
        try {
            const response = await fetch(`../api/coupons.php?id=${couponId}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(data.message || 'Coupon deleted successfully');
                this.loadCoupons();
            } else {
                this.showError(data.message || 'Failed to delete coupon');
            }
        } catch (error) {
            console.error('Error deleting coupon:', error);
            this.showError('Error deleting coupon');
        }
    }

    async viewUsage(couponId) {
        try {
            const response = await fetch(`../api/coupons.php?action=usage&coupon_id=${couponId}`);
            const data = await response.json();
            
            if (data.success) {
                this.showUsageModal(data.data);
            } else {
                this.showError('Failed to load coupon usage');
            }
        } catch (error) {
            console.error('Error loading coupon usage:', error);
            this.showError('Error loading coupon usage');
        }
    }

    showUsageModal(usageData) {
        // Create a simple usage display
        let usageHtml = '<h3>Coupon Usage History</h3>';
        
        if (usageData.length === 0) {
            usageHtml += '<p>No usage history found.</p>';
        } else {
            usageHtml += '<table style="width: 100%; border-collapse: collapse;">';
            usageHtml += '<thead><tr><th>Date</th><th>User</th><th>Order</th><th>Amount</th></tr></thead>';
            usageHtml += '<tbody>';
            
            usageData.forEach(usage => {
                usageHtml += `
                    <tr>
                        <td>${new Date(usage.used_at).toLocaleDateString()}</td>
                        <td>${usage.first_name && usage.last_name ? usage.first_name + ' ' + usage.last_name : 'Guest'}</td>
                        <td>${usage.order_number || 'N/A'}</td>
                        <td>$${parseFloat(usage.total_amount || 0).toFixed(2)}</td>
                    </tr>
                `;
            });
            
            usageHtml += '</tbody></table>';
        }
        
        // Simple alert for now - you could create a proper modal
        const usageWindow = window.open('', '_blank', 'width=600,height=400');
        usageWindow.document.write(`
            <html>
                <head><title>Coupon Usage History</title></head>
                <body style="font-family: Arial, sans-serif; padding: 20px;">
                    ${usageHtml}
                </body>
            </html>
        `);
    }

    showSuccess(message) {
        // You can implement a toast notification system here
        alert(message);
    }

    showError(message) {
        // You can implement a toast notification system here
        alert('Error: ' + message);
    }
}

// Initialize the coupon manager when the page loads
let couponManager;
document.addEventListener('DOMContentLoaded', () => {
    couponManager = new CouponManager();
});
