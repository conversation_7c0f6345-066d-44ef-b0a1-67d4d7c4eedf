// Admin Products Management JavaScript

class ProductManager {
    constructor() {
        this.currentProduct = null;
        this.productImages = [];
        this.categories = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadCategories();
        this.loadProducts();
    }

    bindEvents() {
        // Modal events
        document.getElementById('addProductBtn').addEventListener('click', () => this.openModal());
        document.getElementById('closeModal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        
        // Form submission
        document.getElementById('productForm').addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Image upload tabs
        document.querySelectorAll('.image-upload-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchImageUploadTab(e.target.dataset.tab));
        });
        
        // File upload
        const fileInput = document.getElementById('fileInput');
        const fileUploadArea = document.getElementById('fileUploadArea');
        
        fileUploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        
        // Drag and drop
        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });
        
        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('dragover');
        });
        
        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            this.handleFileUpload(e);
        });
        
        // Base64 upload
        document.getElementById('addBase64Image').addEventListener('click', () => this.handleBase64Upload());
        document.getElementById('base64Input').addEventListener('input', (e) => this.previewBase64(e.target.value));
        
        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('productModal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    async loadCategories() {
        try {
            const response = await fetch('../api/categories.php');
            const data = await response.json();
            
            if (data.success) {
                this.categories = data.data;
                this.populateCategorySelect();
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    populateCategorySelect() {
        const select = document.getElementById('productCategory');
        select.innerHTML = '<option value="">Select Category</option>';
        
        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            select.appendChild(option);
        });
    }

    async loadProducts() {
        const loading = document.getElementById('productsLoading');
        const table = document.getElementById('productsTable');
        
        loading.style.display = 'block';
        table.style.display = 'none';
        
        try {
            const response = await fetch('../api/products.php');
            const data = await response.json();
            
            if (data.success) {
                this.displayProducts(data.data);
            } else {
                this.showError('Failed to load products');
            }
        } catch (error) {
            console.error('Error loading products:', error);
            this.showError('Error loading products');
        } finally {
            loading.style.display = 'none';
            table.style.display = 'table';
        }
    }

    displayProducts(products) {
        const tbody = document.getElementById('productsTableBody');
        tbody.innerHTML = '';
        
        products.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <img src="${product.primary_image || '../images/placeholder.jpg'}" alt="${product.name}" onerror="this.src='../images/placeholder.jpg'">
                </td>
                <td>
                    <div class="product-name">${product.name}</div>
                    <div class="product-category">${product.category_name || 'Uncategorized'}</div>
                </td>
                <td>${product.sku}</td>
                <td>
                    <div>$${parseFloat(product.price).toFixed(2)}</div>
                    ${product.sale_price ? `<div style="text-decoration: line-through; opacity: 0.6;">$${parseFloat(product.sale_price).toFixed(2)}</div>` : ''}
                </td>
                <td>
                    <span class="product-stock ${this.getStockClass(product.stock_quantity, product.stock_status)}">
                        ${product.stock_quantity} ${product.stock_status === 'in_stock' ? 'In Stock' : 'Out of Stock'}
                    </span>
                </td>
                <td>
                    <span class="product-status ${product.status}">${this.capitalizeFirst(product.status)}</span>
                </td>
                <td>
                    <div class="product-actions">
                        <button onclick="productManager.editProduct(${product.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="productManager.manageImages(${product.id})" title="Manage Images">
                            <i class="fas fa-images"></i>
                        </button>
                        <button onclick="productManager.deleteProduct(${product.id})" class="delete-btn" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    getStockClass(quantity, status) {
        if (status === 'out_of_stock' || quantity === 0) return 'out-of-stock';
        if (quantity <= 10) return 'low-stock';
        return 'in-stock';
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    openModal(product = null) {
        this.currentProduct = product;
        const modal = document.getElementById('productModal');
        const title = document.getElementById('modalTitle');
        const form = document.getElementById('productForm');
        
        if (product) {
            title.textContent = 'Edit Product';
            this.populateForm(product);
            this.loadProductImages(product.id);
        } else {
            title.textContent = 'Add Product';
            form.reset();
            this.clearProductImages();
        }
        
        modal.style.display = 'block';
    }

    closeModal() {
        const modal = document.getElementById('productModal');
        modal.style.display = 'none';
        this.currentProduct = null;
        this.clearProductImages();
    }

    populateForm(product) {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);
        
        Object.keys(product).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = product[key] == 1;
                } else {
                    input.value = product[key] || '';
                }
            }
        });
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const productData = {};
        
        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            if (key === 'featured') {
                productData[key] = form.querySelector(`[name="${key}"]`).checked;
            } else {
                productData[key] = value;
            }
        }
        
        try {
            const url = this.currentProduct ? 
                `../api/products.php?id=${this.currentProduct.id}` : 
                '../api/products.php';
            
            const method = this.currentProduct ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(productData)
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(this.currentProduct ? 'Product updated successfully' : 'Product created successfully');
                this.closeModal();
                this.loadProducts();
            } else {
                this.showError(data.message || 'Failed to save product');
            }
        } catch (error) {
            console.error('Error saving product:', error);
            this.showError('Error saving product');
        }
    }

    async editProduct(productId) {
        try {
            const response = await fetch(`../api/products.php?id=${productId}`);
            const data = await response.json();
            
            if (data.success) {
                this.openModal(data.data);
            } else {
                this.showError('Failed to load product details');
            }
        } catch (error) {
            console.error('Error loading product:', error);
            this.showError('Error loading product');
        }
    }

    async deleteProduct(productId) {
        if (!confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
            return;
        }
        
        try {
            const response = await fetch(`../api/products.php?id=${productId}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Product deleted successfully');
                this.loadProducts();
            } else {
                this.showError(data.message || 'Failed to delete product');
            }
        } catch (error) {
            console.error('Error deleting product:', error);
            this.showError('Error deleting product');
        }
    }

    switchImageUploadTab(tab) {
        // Update tab buttons
        document.querySelectorAll('.image-upload-tab').forEach(t => t.classList.remove('active'));
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.image-upload-content').forEach(c => c.classList.remove('active'));
        document.getElementById(`${tab}UploadTab`).classList.add('active');
    }

    handleFileUpload(e) {
        const files = e.dataTransfer ? e.dataTransfer.files : e.target.files;
        
        if (!this.currentProduct) {
            this.showError('Please save the product first before adding images');
            return;
        }
        
        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/')) {
                this.uploadFile(file);
            }
        });
    }

    async uploadFile(file) {
        const formData = new FormData();
        formData.append('action', 'upload_file');
        formData.append('product_id', this.currentProduct.id);
        formData.append('image', file);
        
        try {
            const response = await fetch('../api/product-images.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.loadProductImages(this.currentProduct.id);
                this.showSuccess('Image uploaded successfully');
            } else {
                this.showError(data.message || 'Failed to upload image');
            }
        } catch (error) {
            console.error('Error uploading image:', error);
            this.showError('Error uploading image');
        }
    }

    previewBase64(base64Data) {
        const preview = document.getElementById('base64Preview');
        
        if (base64Data && base64Data.startsWith('data:image/')) {
            preview.src = base64Data;
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    }

    async handleBase64Upload() {
        const base64Input = document.getElementById('base64Input');
        const altTextInput = document.getElementById('base64AltText');
        
        if (!this.currentProduct) {
            this.showError('Please save the product first before adding images');
            return;
        }
        
        if (!base64Input.value.trim()) {
            this.showError('Please enter base64 image data');
            return;
        }
        
        try {
            const response = await fetch('../api/product-images.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'upload_base64',
                    product_id: this.currentProduct.id,
                    image_data: base64Input.value.trim(),
                    alt_text: altTextInput.value.trim()
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.loadProductImages(this.currentProduct.id);
                this.showSuccess('Image uploaded successfully');
                base64Input.value = '';
                altTextInput.value = '';
                document.getElementById('base64Preview').style.display = 'none';
            } else {
                this.showError(data.message || 'Failed to upload image');
            }
        } catch (error) {
            console.error('Error uploading base64 image:', error);
            this.showError('Error uploading image');
        }
    }

    async loadProductImages(productId) {
        try {
            const response = await fetch(`../api/product-images.php?product_id=${productId}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayProductImages(data.data);
            }
        } catch (error) {
            console.error('Error loading product images:', error);
        }
    }

    displayProductImages(images) {
        const gallery = document.getElementById('productImagesGallery');
        gallery.innerHTML = '';
        
        images.forEach(image => {
            const imageItem = document.createElement('div');
            imageItem.className = 'image-item';
            imageItem.innerHTML = `
                ${image.is_primary ? '<div class="primary-badge">Primary</div>' : ''}
                <img src="${image.image_path}" alt="${image.alt_text || 'Product image'}">
                <div class="image-item-controls">
                    <button onclick="productManager.setPrimaryImage(${image.id})" title="Set as Primary">
                        <i class="fas fa-star"></i>
                    </button>
                    <button onclick="productManager.deleteImage(${image.id})" title="Delete Image">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            gallery.appendChild(imageItem);
        });
    }

    async setPrimaryImage(imageId) {
        try {
            const response = await fetch(`../api/product-images.php?id=${imageId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'set_primary'
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.loadProductImages(this.currentProduct.id);
                this.showSuccess('Primary image updated');
            } else {
                this.showError(data.message || 'Failed to update primary image');
            }
        } catch (error) {
            console.error('Error setting primary image:', error);
            this.showError('Error setting primary image');
        }
    }

    async deleteImage(imageId) {
        if (!confirm('Are you sure you want to delete this image?')) {
            return;
        }
        
        try {
            const response = await fetch(`../api/product-images.php?id=${imageId}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.loadProductImages(this.currentProduct.id);
                this.showSuccess('Image deleted successfully');
            } else {
                this.showError(data.message || 'Failed to delete image');
            }
        } catch (error) {
            console.error('Error deleting image:', error);
            this.showError('Error deleting image');
        }
    }

    clearProductImages() {
        document.getElementById('productImagesGallery').innerHTML = '';
        document.getElementById('base64Input').value = '';
        document.getElementById('base64AltText').value = '';
        document.getElementById('base64Preview').style.display = 'none';
    }

    manageImages(productId) {
        // For now, just open the edit modal which includes image management
        this.editProduct(productId);
    }

    showSuccess(message) {
        // You can implement a toast notification system here
        alert(message);
    }

    showError(message) {
        // You can implement a toast notification system here
        alert('Error: ' + message);
    }
}

// Initialize the product manager when the page loads
let productManager;
document.addEventListener('DOMContentLoaded', () => {
    productManager = new ProductManager();
});
