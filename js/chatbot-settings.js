// Chatbot Settings Management JavaScript

class ChatbotSettings {
    constructor() {
        this.currentSettings = {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSettings();
    }

    bindEvents() {
        // Form submission
        document.getElementById('chatbotSettingsForm').addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Reset button
        document.getElementById('resetBtn').addEventListener('click', () => this.resetToDefaults());
        
        // Test chatbot
        document.getElementById('testSend').addEventListener('click', () => this.sendTestMessage());
        document.getElementById('testInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendTestMessage();
            }
        });
        
        // Enable test input when API key is provided
        document.getElementById('apiKey').addEventListener('input', () => this.updateTestAvailability());
        document.getElementById('enabled').addEventListener('change', () => this.updateTestAvailability());
    }

    async loadSettings() {
        const loading = document.getElementById('settingsLoading');
        const form = document.getElementById('chatbotSettingsForm');
        
        loading.style.display = 'block';
        form.style.display = 'none';
        
        try {
            const response = await fetch('../api/chatbot.php?action=settings');
            const data = await response.json();
            
            if (data.success) {
                this.currentSettings = data.data;
                this.populateForm(data.data);
                this.updateStatus(data.data);
            } else {
                this.showError('Failed to load settings');
            }
        } catch (error) {
            console.error('Error loading settings:', error);
            this.showError('Error loading settings');
        } finally {
            loading.style.display = 'none';
            form.style.display = 'block';
        }
    }

    populateForm(settings) {
        // Basic settings
        document.getElementById('enabled').checked = settings.enabled || false;
        document.getElementById('welcomeMessage').value = settings.welcome_message || '';
        
        // API configuration
        if (settings.api_key_set) {
            document.getElementById('apiKey').placeholder = 'API key is set (enter new key to change)';
        }
        document.getElementById('model').value = settings.model || 'meta-llama/llama-3.2-3b-instruct:free';
        document.getElementById('apiUrl').value = settings.api_url || 'https://openrouter.ai/api/v1/chat/completions';
        
        // Advanced settings
        document.getElementById('maxTokens').value = settings.max_tokens || 500;
        document.getElementById('temperature').value = settings.temperature || 0.7;
        
        this.updateTestAvailability();
    }

    updateStatus(settings) {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        const isEnabled = settings.enabled && settings.api_key_set;
        
        if (isEnabled) {
            statusIndicator.className = 'status-indicator enabled';
            statusText.textContent = 'Enabled';
        } else {
            statusIndicator.className = 'status-indicator disabled';
            statusText.textContent = 'Disabled';
        }
    }

    updateTestAvailability() {
        const testInput = document.getElementById('testInput');
        const testSend = document.getElementById('testSend');
        const enabled = document.getElementById('enabled').checked;
        const hasApiKey = document.getElementById('apiKey').value.trim() || this.currentSettings.api_key_set;
        
        const canTest = enabled && hasApiKey;
        
        testInput.disabled = !canTest;
        testSend.disabled = !canTest;
        
        if (!canTest) {
            testInput.placeholder = enabled ? 'Enter API key to test' : 'Enable chatbot to test';
        } else {
            testInput.placeholder = 'Type a test message...';
        }
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const settings = {};
        
        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            if (key === 'enabled') {
                settings[key] = true;
            } else if (value.trim() !== '') {
                settings[key] = value.trim();
            }
        }
        
        // Handle unchecked checkbox
        if (!settings.enabled) {
            settings.enabled = false;
        }
        
        // Convert numeric values
        if (settings.max_tokens) {
            settings.max_tokens = parseInt(settings.max_tokens);
        }
        if (settings.temperature) {
            settings.temperature = parseFloat(settings.temperature);
        }
        
        try {
            const response = await fetch('../api/chatbot.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update_settings',
                    ...settings
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Settings saved successfully');
                this.loadSettings(); // Reload to get updated status
            } else {
                this.showError(data.message || 'Failed to save settings');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showError('Error saving settings');
        }
    }

    resetToDefaults() {
        if (!confirm('Are you sure you want to reset all settings to defaults? This will clear your API key.')) {
            return;
        }
        
        // Reset form to default values
        document.getElementById('enabled').checked = false;
        document.getElementById('welcomeMessage').value = "Hello! I'm here to help you with any questions about our anime merchandise. How can I assist you today?";
        document.getElementById('apiKey').value = '';
        document.getElementById('apiKey').placeholder = 'Enter your OpenRouter API key';
        document.getElementById('model').value = 'meta-llama/llama-3.2-3b-instruct:free';
        document.getElementById('apiUrl').value = 'https://openrouter.ai/api/v1/chat/completions';
        document.getElementById('maxTokens').value = 500;
        document.getElementById('temperature').value = 0.7;
        
        this.updateTestAvailability();
    }

    async sendTestMessage() {
        const input = document.getElementById('testInput');
        const message = input.value.trim();
        
        if (!message) return;
        
        // Add user message to test chat
        this.addTestMessage(message, 'user');
        input.value = '';
        
        // Show typing indicator
        this.showTestTyping();
        
        try {
            const response = await fetch('../api/chatbot.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'send_message',
                    message: message
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.addTestMessage(data.message, 'bot');
            } else {
                this.addTestMessage(data.message || 'Error: Could not get response', 'bot');
            }
        } catch (error) {
            console.error('Error testing chatbot:', error);
            this.addTestMessage('Error: Connection failed', 'bot');
        } finally {
            this.hideTestTyping();
        }
    }

    addTestMessage(text, sender) {
        const testChat = document.getElementById('testChat');
        const messageDiv = document.createElement('div');
        messageDiv.className = `test-message ${sender}`;
        
        messageDiv.innerHTML = `
            <div class="test-message-content">${this.escapeHtml(text)}</div>
        `;
        
        testChat.appendChild(messageDiv);
        testChat.scrollTop = testChat.scrollHeight;
    }

    showTestTyping() {
        const testChat = document.getElementById('testChat');
        const typingDiv = document.createElement('div');
        typingDiv.className = 'test-message bot typing-indicator';
        typingDiv.id = 'testTyping';
        typingDiv.innerHTML = `
            <div class="test-message-content">
                <i class="fas fa-circle"></i>
                <i class="fas fa-circle"></i>
                <i class="fas fa-circle"></i>
            </div>
        `;
        
        testChat.appendChild(typingDiv);
        testChat.scrollTop = testChat.scrollHeight;
    }

    hideTestTyping() {
        const typing = document.getElementById('testTyping');
        if (typing) {
            typing.remove();
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showSuccess(message) {
        // You can implement a toast notification system here
        alert(message);
    }

    showError(message) {
        // You can implement a toast notification system here
        alert('Error: ' + message);
    }
}

// Initialize chatbot settings when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ChatbotSettings();
});
