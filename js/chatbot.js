// Chatbot Integration JavaScript

class ChatbotWidget {
    constructor() {
        this.isOpen = false;
        this.isEnabled = false;
        this.conversationId = null;
        this.messages = [];
        this.isTyping = false;
        this.init();
    }

    async init() {
        await this.checkStatus();
        if (this.isEnabled) {
            this.createWidget();
            this.bindEvents();
        }
    }

    async checkStatus() {
        try {
            const response = await fetch('../api/chatbot.php?action=status');
            const data = await response.json();
            
            if (data.success) {
                this.isEnabled = data.enabled;
                this.welcomeMessage = data.welcome_message;
            }
        } catch (error) {
            console.error('Error checking chatbot status:', error);
        }
    }

    createWidget() {
        // Create chatbot widget HTML
        const widget = document.createElement('div');
        widget.id = 'chatbot-widget';
        widget.className = 'chatbot-widget';
        widget.innerHTML = `
            <div class="chatbot-toggle" id="chatbotToggle">
                <i class="fas fa-comments"></i>
                <span class="chatbot-badge" id="chatbotBadge" style="display: none;">1</span>
            </div>
            
            <div class="chatbot-window" id="chatbotWindow">
                <div class="chatbot-header">
                    <div class="chatbot-title">
                        <i class="fas fa-robot"></i>
                        <span>Infinite Shadow Assistant</span>
                    </div>
                    <div class="chatbot-controls">
                        <button class="chatbot-minimize" id="chatbotMinimize">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button class="chatbot-close" id="chatbotClose">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="chatbot-messages" id="chatbotMessages">
                    <div class="chatbot-message bot-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">${this.welcomeMessage}</div>
                            <div class="message-time">${this.formatTime(new Date())}</div>
                        </div>
                    </div>
                </div>
                
                <div class="chatbot-typing" id="chatbotTyping" style="display: none;">
                    <div class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span>Assistant is typing...</span>
                </div>
                
                <div class="chatbot-input">
                    <div class="input-container">
                        <input type="text" id="chatbotInput" placeholder="Type your message..." maxlength="500">
                        <button id="chatbotSend" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(widget);
        this.addStyles();
    }

    addStyles() {
        const styles = `
            .chatbot-widget {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 10000;
                font-family: 'Poppins', sans-serif;
            }
            
            .chatbot-toggle {
                width: 60px;
                height: 60px;
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                transition: all 0.3s ease;
                position: relative;
            }
            
            .chatbot-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
            }
            
            .chatbot-toggle i {
                color: white;
                font-size: 1.5rem;
            }
            
            .chatbot-badge {
                position: absolute;
                top: -5px;
                right: -5px;
                background-color: var(--secondary-color);
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.7rem;
                font-weight: 600;
            }
            
            .chatbot-window {
                position: absolute;
                bottom: 80px;
                right: 0;
                width: 350px;
                height: 500px;
                background-color: var(--card-bg);
                border-radius: 15px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
                display: none;
                flex-direction: column;
                overflow: hidden;
                border: 1px solid var(--border-color);
            }
            
            .chatbot-header {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                color: white;
                padding: 15px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .chatbot-title {
                display: flex;
                align-items: center;
                gap: 10px;
                font-weight: 600;
            }
            
            .chatbot-controls {
                display: flex;
                gap: 10px;
            }
            
            .chatbot-controls button {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 5px;
                border-radius: 3px;
                transition: background-color 0.3s ease;
            }
            
            .chatbot-controls button:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
            
            .chatbot-messages {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                gap: 15px;
            }
            
            .chatbot-message {
                display: flex;
                gap: 10px;
                max-width: 85%;
            }
            
            .chatbot-message.user-message {
                align-self: flex-end;
                flex-direction: row-reverse;
            }
            
            .message-avatar {
                width: 35px;
                height: 35px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
            }
            
            .bot-message .message-avatar {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                color: white;
            }
            
            .user-message .message-avatar {
                background-color: var(--border-color);
                color: var(--text-color);
            }
            
            .message-content {
                flex: 1;
            }
            
            .message-text {
                background-color: var(--bg-color);
                padding: 12px 15px;
                border-radius: 15px;
                line-height: 1.4;
                word-wrap: break-word;
            }
            
            .user-message .message-text {
                background-color: var(--primary-color);
                color: white;
            }
            
            .message-time {
                font-size: 0.7rem;
                color: var(--text-color);
                opacity: 0.6;
                margin-top: 5px;
                text-align: right;
            }
            
            .user-message .message-time {
                text-align: left;
            }
            
            .chatbot-typing {
                padding: 10px 20px;
                display: flex;
                align-items: center;
                gap: 10px;
                color: var(--text-color);
                opacity: 0.7;
                font-size: 0.9rem;
            }
            
            .typing-indicator {
                display: flex;
                gap: 3px;
            }
            
            .typing-indicator span {
                width: 6px;
                height: 6px;
                background-color: var(--primary-color);
                border-radius: 50%;
                animation: typing 1.4s infinite ease-in-out;
            }
            
            .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
            .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }
            
            @keyframes typing {
                0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
                40% { transform: scale(1); opacity: 1; }
            }
            
            .chatbot-input {
                padding: 20px;
                border-top: 1px solid var(--border-color);
            }
            
            .input-container {
                display: flex;
                gap: 10px;
                align-items: center;
            }
            
            .chatbot-input input {
                flex: 1;
                padding: 12px 15px;
                border: 1px solid var(--border-color);
                border-radius: 25px;
                background-color: var(--bg-color);
                color: var(--text-color);
                outline: none;
                font-family: inherit;
            }
            
            .chatbot-input input:focus {
                border-color: var(--primary-color);
            }
            
            .chatbot-input button {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                border: none;
                background-color: var(--primary-color);
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            }
            
            .chatbot-input button:disabled {
                background-color: var(--border-color);
                cursor: not-allowed;
            }
            
            .chatbot-input button:not(:disabled):hover {
                background-color: var(--secondary-color);
                transform: scale(1.1);
            }
            
            @media (max-width: 768px) {
                .chatbot-widget {
                    bottom: 10px;
                    right: 10px;
                }
                
                .chatbot-window {
                    width: calc(100vw - 20px);
                    height: calc(100vh - 100px);
                    bottom: 70px;
                    right: -10px;
                }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    bindEvents() {
        const toggle = document.getElementById('chatbotToggle');
        const window = document.getElementById('chatbotWindow');
        const minimize = document.getElementById('chatbotMinimize');
        const close = document.getElementById('chatbotClose');
        const input = document.getElementById('chatbotInput');
        const send = document.getElementById('chatbotSend');

        toggle.addEventListener('click', () => this.toggleWidget());
        minimize.addEventListener('click', () => this.minimizeWidget());
        close.addEventListener('click', () => this.closeWidget());

        input.addEventListener('input', (e) => {
            send.disabled = e.target.value.trim().length === 0;
        });

        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !send.disabled) {
                this.sendMessage();
            }
        });

        send.addEventListener('click', () => this.sendMessage());
    }

    toggleWidget() {
        const window = document.getElementById('chatbotWindow');
        
        if (this.isOpen) {
            this.minimizeWidget();
        } else {
            this.openWidget();
        }
    }

    openWidget() {
        const window = document.getElementById('chatbotWindow');
        const badge = document.getElementById('chatbotBadge');
        
        window.style.display = 'flex';
        badge.style.display = 'none';
        this.isOpen = true;
        
        // Focus input
        setTimeout(() => {
            document.getElementById('chatbotInput').focus();
        }, 100);
    }

    minimizeWidget() {
        const window = document.getElementById('chatbotWindow');
        window.style.display = 'none';
        this.isOpen = false;
    }

    closeWidget() {
        this.minimizeWidget();
    }

    async sendMessage() {
        const input = document.getElementById('chatbotInput');
        const message = input.value.trim();
        
        if (!message || this.isTyping) return;

        // Add user message to UI
        this.addMessage(message, 'user');
        input.value = '';
        document.getElementById('chatbotSend').disabled = true;

        // Show typing indicator
        this.showTyping();

        try {
            const response = await fetch('../api/chatbot.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'send_message',
                    message: message,
                    conversation_id: this.conversationId
                })
            });

            const data = await response.json();

            if (data.success) {
                this.conversationId = data.conversation_id;
                this.addMessage(data.message, 'bot');
            } else {
                this.addMessage(data.message || 'Sorry, I encountered an error. Please try again.', 'bot');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            this.addMessage('Sorry, I\'m having trouble connecting. Please try again later.', 'bot');
        } finally {
            this.hideTyping();
        }
    }

    addMessage(text, sender) {
        const messagesContainer = document.getElementById('chatbotMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `chatbot-message ${sender}-message`;
        
        const avatar = sender === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
        
        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-text">${this.escapeHtml(text)}</div>
                <div class="message-time">${this.formatTime(new Date())}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Show notification badge if widget is closed
        if (!this.isOpen && sender === 'bot') {
            this.showNotificationBadge();
        }
    }

    showTyping() {
        const typing = document.getElementById('chatbotTyping');
        typing.style.display = 'flex';
        this.isTyping = true;
        
        const messagesContainer = document.getElementById('chatbotMessages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    hideTyping() {
        const typing = document.getElementById('chatbotTyping');
        typing.style.display = 'none';
        this.isTyping = false;
    }

    showNotificationBadge() {
        const badge = document.getElementById('chatbotBadge');
        badge.style.display = 'flex';
    }

    formatTime(date) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if not in admin panel
    if (!window.location.pathname.includes('/admin/')) {
        window.chatbot = new ChatbotWidget();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatbotWidget;
}
