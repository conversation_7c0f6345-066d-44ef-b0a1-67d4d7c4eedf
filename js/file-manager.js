// File Manager JavaScript

class FileManager {
    constructor() {
        this.selectedFiles = new Set();
        this.currentFiles = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadStorageStats();
        this.loadFiles();
    }

    bindEvents() {
        // Upload events
        document.getElementById('uploadBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });
        
        // Drag and drop
        const uploadArea = document.getElementById('fileUploadArea');
        
        uploadArea.addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFileUpload(e.dataTransfer.files);
        });
        
        // Filter and search events
        document.getElementById('fileTypeFilter').addEventListener('change', () => this.loadFiles());
        document.getElementById('sortFiles').addEventListener('change', () => this.loadFiles());
        document.getElementById('searchFiles').addEventListener('input', this.debounce(() => this.loadFiles(), 500));
        
        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadStorageStats();
            this.loadFiles();
        });
        
        // Modal events
        document.getElementById('closeModal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        document.getElementById('fileForm').addEventListener('submit', (e) => this.handleFormSubmit(e));
        document.getElementById('downloadBtn').addEventListener('click', () => this.downloadCurrentFile());
        
        // Delete selected button
        document.getElementById('deleteSelectedBtn').addEventListener('click', () => this.deleteSelectedFiles());
        
        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('fileModal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    async loadStorageStats() {
        try {
            const response = await fetch('../api/files.php?action=stats');
            const data = await response.json();
            
            if (data.success) {
                this.displayStorageStats(data.data);
            }
        } catch (error) {
            console.error('Error loading storage stats:', error);
        }
    }

    displayStorageStats(stats) {
        const container = document.getElementById('storageStats');
        container.innerHTML = `
            <div class="stat-card">
                <i class="fas fa-file"></i>
                <div class="stat-value">${stats.total_files}</div>
                <div class="stat-label">Total Files</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-hdd"></i>
                <div class="stat-value">${stats.total_size_formatted}</div>
                <div class="stat-label">Total Size</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-chart-bar"></i>
                <div class="stat-value">${stats.average_size_formatted}</div>
                <div class="stat-label">Average Size</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-images"></i>
                <div class="stat-value">${this.getTypeCount(stats.by_type, 'image')}</div>
                <div class="stat-label">Images</div>
            </div>
        `;
    }

    getTypeCount(typeStats, type) {
        const typeData = typeStats.find(t => t.file_type === type);
        return typeData ? typeData.count : 0;
    }

    async loadFiles() {
        const loading = document.getElementById('filesLoading');
        const grid = document.getElementById('filesGrid');
        
        loading.style.display = 'block';
        grid.style.display = 'none';
        
        try {
            const filters = this.getFilters();
            const queryString = new URLSearchParams(filters).toString();
            
            const response = await fetch(`../api/files.php?${queryString}`);
            const data = await response.json();
            
            if (data.success) {
                this.currentFiles = data.data;
                this.displayFiles(data.data);
            } else {
                this.showError('Failed to load files');
            }
        } catch (error) {
            console.error('Error loading files:', error);
            this.showError('Error loading files');
        } finally {
            loading.style.display = 'none';
            grid.style.display = 'grid';
        }
    }

    getFilters() {
        return {
            file_type: document.getElementById('fileTypeFilter').value,
            search: document.getElementById('searchFiles').value,
            sort: document.getElementById('sortFiles').value
        };
    }

    displayFiles(files) {
        const grid = document.getElementById('filesGrid');
        grid.innerHTML = '';
        
        if (files.length === 0) {
            grid.innerHTML = '<div style="grid-column: 1 / -1; text-align: center; padding: 40px; opacity: 0.7;">No files found</div>';
            return;
        }
        
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.dataset.fileId = file.id;
            
            const isImage = file.file_type === 'image';
            const iconOrThumbnail = isImage ? 
                `<img src="${file.thumbnail_url || file.file_url}" alt="${file.original_filename}" class="file-thumbnail" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                 <div class="file-icon" style="display: none;"><i class="fas fa-file-image"></i></div>` :
                `<div class="file-icon"><i class="${this.getFileIcon(file.file_type)}"></i></div>`;
            
            fileItem.innerHTML = `
                ${iconOrThumbnail}
                <div class="file-name" title="${file.original_filename}">${this.truncateFileName(file.original_filename, 20)}</div>
                <div class="file-info">
                    <div>${file.formatted_size}</div>
                    <div>${new Date(file.created_at).toLocaleDateString()}</div>
                    <div>by ${file.uploader_name}</div>
                </div>
                <div class="file-actions">
                    <button onclick="fileManager.viewFile(${file.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="fileManager.downloadFile(${file.id})" title="Download">
                        <i class="fas fa-download"></i>
                    </button>
                    <button onclick="fileManager.selectFile(${file.id})" title="Select">
                        <i class="fas fa-check"></i>
                    </button>
                    <button onclick="fileManager.deleteFile(${file.id})" class="delete-btn" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            
            // Add click event for selection
            fileItem.addEventListener('click', (e) => {
                if (!e.target.closest('.file-actions')) {
                    this.toggleFileSelection(file.id);
                }
            });
            
            grid.appendChild(fileItem);
        });
        
        this.updateSelectedActions();
    }

    getFileIcon(fileType) {
        const icons = {
            document: 'fas fa-file-alt',
            spreadsheet: 'fas fa-file-excel',
            presentation: 'fas fa-file-powerpoint',
            archive: 'fas fa-file-archive',
            video: 'fas fa-file-video',
            audio: 'fas fa-file-audio',
            image: 'fas fa-file-image'
        };
        
        return icons[fileType] || 'fas fa-file';
    }

    truncateFileName(filename, maxLength) {
        if (filename.length <= maxLength) return filename;
        
        const extension = filename.split('.').pop();
        const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
        const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
        
        return truncatedName + '.' + extension;
    }

    async handleFileUpload(files) {
        if (files.length === 0) return;
        
        const formData = new FormData();
        
        if (files.length === 1) {
            formData.append('action', 'upload');
            formData.append('file', files[0]);
        } else {
            formData.append('action', 'upload_multiple');
            Array.from(files).forEach(file => {
                formData.append('files[]', file);
            });
        }
        
        try {
            const response = await fetch('../api/files.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(files.length === 1 ? 'File uploaded successfully' : `${data.summary?.success || 0} files uploaded successfully`);
                this.loadStorageStats();
                this.loadFiles();
            } else {
                this.showError(data.message || 'Failed to upload files');
            }
        } catch (error) {
            console.error('Error uploading files:', error);
            this.showError('Error uploading files');
        }
        
        // Reset file input
        document.getElementById('fileInput').value = '';
    }

    toggleFileSelection(fileId) {
        const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
        
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
            fileItem.classList.remove('selected');
        } else {
            this.selectedFiles.add(fileId);
            fileItem.classList.add('selected');
        }
        
        this.updateSelectedActions();
    }

    selectFile(fileId) {
        this.toggleFileSelection(fileId);
    }

    updateSelectedActions() {
        const selectedActions = document.querySelector('.selected-actions');
        const deleteBtn = document.getElementById('deleteSelectedBtn');
        
        if (this.selectedFiles.size > 0) {
            selectedActions.style.display = 'block';
            deleteBtn.innerHTML = `<i class="fas fa-trash"></i> Delete Selected (${this.selectedFiles.size})`;
        } else {
            selectedActions.style.display = 'none';
        }
    }

    async viewFile(fileId) {
        try {
            const response = await fetch(`../api/files.php?id=${fileId}`);
            const data = await response.json();
            
            if (data.success) {
                this.openModal(data.data);
            } else {
                this.showError('Failed to load file details');
            }
        } catch (error) {
            console.error('Error loading file:', error);
            this.showError('Error loading file');
        }
    }

    openModal(file) {
        this.currentFile = file;
        const modal = document.getElementById('fileModal');
        
        // Populate form
        document.getElementById('fileId').value = file.id;
        document.getElementById('fileName').value = file.original_filename;
        document.getElementById('fileDescription').value = file.description || '';
        document.getElementById('filePublic').checked = file.is_public == 1;
        
        // Display file info
        const fileInfo = document.getElementById('fileInfo');
        fileInfo.innerHTML = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9rem;">
                <div><strong>Type:</strong> ${file.file_type}</div>
                <div><strong>Size:</strong> ${file.formatted_size}</div>
                <div><strong>Uploaded:</strong> ${new Date(file.created_at).toLocaleDateString()}</div>
                <div><strong>MIME Type:</strong> ${file.mime_type}</div>
            </div>
        `;
        
        modal.style.display = 'block';
    }

    closeModal() {
        const modal = document.getElementById('fileModal');
        modal.style.display = 'none';
        this.currentFile = null;
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const updateData = {
            description: formData.get('description'),
            is_public: formData.get('is_public') ? true : false
        };
        
        try {
            const response = await fetch(`../api/files.php?id=${this.currentFile.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('File updated successfully');
                this.closeModal();
                this.loadFiles();
            } else {
                this.showError(data.message || 'Failed to update file');
            }
        } catch (error) {
            console.error('Error updating file:', error);
            this.showError('Error updating file');
        }
    }

    downloadCurrentFile() {
        if (this.currentFile) {
            this.downloadFile(this.currentFile.id);
        }
    }

    downloadFile(fileId) {
        window.open(`../api/files.php?action=download&id=${fileId}`, '_blank');
    }

    async deleteFile(fileId) {
        if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
            return;
        }
        
        try {
            const response = await fetch(`../api/files.php?id=${fileId}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('File deleted successfully');
                this.selectedFiles.delete(fileId);
                this.loadStorageStats();
                this.loadFiles();
            } else {
                this.showError(data.message || 'Failed to delete file');
            }
        } catch (error) {
            console.error('Error deleting file:', error);
            this.showError('Error deleting file');
        }
    }

    async deleteSelectedFiles() {
        if (this.selectedFiles.size === 0) return;
        
        if (!confirm(`Are you sure you want to delete ${this.selectedFiles.size} selected files? This action cannot be undone.`)) {
            return;
        }
        
        const deletePromises = Array.from(this.selectedFiles).map(fileId => 
            fetch(`../api/files.php?id=${fileId}`, { method: 'DELETE' })
        );
        
        try {
            const responses = await Promise.all(deletePromises);
            const results = await Promise.all(responses.map(r => r.json()));
            
            const successCount = results.filter(r => r.success).length;
            const errorCount = results.length - successCount;
            
            if (successCount > 0) {
                this.showSuccess(`${successCount} files deleted successfully`);
                if (errorCount > 0) {
                    this.showError(`${errorCount} files failed to delete`);
                }
            } else {
                this.showError('Failed to delete files');
            }
            
            this.selectedFiles.clear();
            this.loadStorageStats();
            this.loadFiles();
        } catch (error) {
            console.error('Error deleting files:', error);
            this.showError('Error deleting files');
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showSuccess(message) {
        // You can implement a toast notification system here
        alert(message);
    }

    showError(message) {
        // You can implement a toast notification system here
        alert('Error: ' + message);
    }
}

// Initialize the file manager when the page loads
let fileManager;
document.addEventListener('DOMContentLoaded', () => {
    fileManager = new FileManager();
});
