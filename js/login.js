document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality with smooth transitions
    const tabs = document.querySelectorAll('.auth-tab');
    const forms = document.querySelectorAll('.auth-form');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const target = tab.getAttribute('data-tab');
            
            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            // Show corresponding form
            forms.forEach(form => form.classList.remove('active'));
            document.getElementById(`${target}-form`).classList.add('active');
        });
    });
    
    // Enhanced password visibility toggle with animation
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', () => {
            const passwordField = button.previousElementSibling;
            
            // Toggle password visibility with animation
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                button.classList.remove('fa-eye');
                button.classList.add('fa-eye-slash');
                button.style.color = 'var(--primary-color)';
                // Add a subtle animation
                button.animate([{transform: 'scale(1)'}, {transform: 'scale(1.2)'}, {transform: 'scale(1)'}], {
                    duration: 300
                });
            } else {
                passwordField.type = 'password';
                button.classList.remove('fa-eye-slash');
                button.classList.add('fa-eye');
                button.style.color = 'var(--text-color)';
                button.style.opacity = '0.7';
            }
        });
    });
    
    // Form validation
    const loginForm = document.getElementById('login-form');
    const signupForm = document.getElementById('signup-form');
    
    // Login form validation
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        let isValid = true;
        
        // Email validation
        const email = document.getElementById('login-email');
        const emailError = email.nextElementSibling;
        
        if (!email.value.trim()) {
            showError(email, emailError, 'Email is required');
            isValid = false;
        } else if (!isValidEmail(email.value)) {
            showError(email, emailError, 'Please enter a valid email address');
            isValid = false;
        } else {
            clearError(email, emailError);
        }
        
        // Password validation
        const password = document.getElementById('login-password');
        const passwordError = password.parentElement.nextElementSibling;
        
        if (!password.value.trim()) {
            showError(password, passwordError, 'Password is required');
            isValid = false;
        } else {
            clearError(password, passwordError);
        }
        
        if (isValid) {
            // Simulate login success
            showSuccessMessage('Login successful! Redirecting...');
            
            // In a real application, you would send the form data to a server here
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }
    });
    
    // Signup form validation
    signupForm.addEventListener('submit', function(e) {
        e.preventDefault();
        let isValid = true;
        
        // Name validation
        const name = document.getElementById('signup-name');
        const nameError = name.nextElementSibling;
        
        if (!name.value.trim()) {
            showError(name, nameError, 'Name is required');
            isValid = false;
        } else {
            clearError(name, nameError);
        }
        
        // Email validation
        const email = document.getElementById('signup-email');
        const emailError = email.nextElementSibling;
        
        if (!email.value.trim()) {
            showError(email, emailError, 'Email is required');
            isValid = false;
        } else if (!isValidEmail(email.value)) {
            showError(email, emailError, 'Please enter a valid email address');
            isValid = false;
        } else {
            clearError(email, emailError);
        }
        
        // Password validation
        const password = document.getElementById('signup-password');
        const passwordError = password.parentElement.nextElementSibling;
        
        if (!password.value.trim()) {
            showError(password, passwordError, 'Password is required');
            isValid = false;
        } else if (password.value.length < 8) {
            showError(password, passwordError, 'Password must be at least 8 characters');
            isValid = false;
        } else {
            clearError(password, passwordError);
        }
        
        // Confirm password validation
        const confirmPassword = document.getElementById('signup-confirm-password');
        const confirmPasswordError = confirmPassword.parentElement.nextElementSibling;
        
        if (!confirmPassword.value.trim()) {
            showError(confirmPassword, confirmPasswordError, 'Please confirm your password');
            isValid = false;
        } else if (confirmPassword.value !== password.value) {
            showError(confirmPassword, confirmPasswordError, 'Passwords do not match');
            isValid = false;
        } else {
            clearError(confirmPassword, confirmPasswordError);
        }
        
        // Terms agreement validation
        const agreeTerms = document.getElementById('agree-terms');
        
        if (!agreeTerms.checked) {
            alert('Please agree to the Terms & Conditions');
            isValid = false;
        }
        
        if (isValid) {
            // Simulate signup success
            showSuccessMessage('Account created successfully! Redirecting...');
            
            // In a real application, you would send the form data to a server here
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }
    });
    
    // Helper functions
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function showError(input, errorElement, message) {
        input.classList.add('error');
        errorElement.textContent = message;
        errorElement.style.color = 'var(--error-color, #ff3860)';
        errorElement.style.fontSize = '0.8rem';
        errorElement.style.marginTop = '5px';
        errorElement.style.display = 'block';
    }
    
    function clearError(input, errorElement) {
        input.classList.remove('error');
        errorElement.textContent = '';
        errorElement.style.display = 'none';
    }
    
    function showSuccessMessage(message) {
        const successMessage = document.createElement('div');
        successMessage.className = 'success-message';
        successMessage.textContent = message;
        successMessage.style.backgroundColor = 'var(--success-color, #48c774)';
        successMessage.style.color = 'white';
        successMessage.style.padding = '15px';
        successMessage.style.borderRadius = '5px';
        successMessage.style.textAlign = 'center';
        successMessage.style.marginTop = '20px';
        
        // Add to both forms to ensure it's visible regardless of which form is active
        const activeForm = document.querySelector('.auth-form.active');
        activeForm.appendChild(successMessage);
    }
});