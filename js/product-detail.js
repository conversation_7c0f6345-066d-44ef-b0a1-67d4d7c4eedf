// Product Detail Page JavaScript

class ProductDetail {
    constructor() {
        this.currentProduct = null;
        this.quantity = 1;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadProductFromUrl();
        this.loadRelatedProducts();
    }

    bindEvents() {
        // Quantity controls
        window.increaseQuantity = () => this.increaseQuantity();
        window.decreaseQuantity = () => this.decreaseQuantity();
        window.changeMainImage = (thumbnail) => this.changeMainImage(thumbnail);
        window.addToCart = () => this.addToCart();
        window.addToWishlist = () => this.addToWishlist();

        // Quantity input change
        const quantityInput = document.getElementById('quantity');
        if (quantityInput) {
            quantityInput.addEventListener('change', (e) => {
                this.quantity = parseInt(e.target.value) || 1;
                this.updateQuantityDisplay();
            });
        }

        // Size selection change
        const sizeSelect = document.getElementById('size');
        if (sizeSelect) {
            sizeSelect.addEventListener('change', () => {
                this.updatePrice();
            });
        }
    }

    loadProductFromUrl() {
        // Get product ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id');
        const productSlug = urlParams.get('slug');

        if (productId || productSlug) {
            this.loadProduct(productId, productSlug);
        } else {
            // Load default product for demo
            this.loadDefaultProduct();
        }
    }

    async loadProduct(productId, productSlug) {
        try {
            let url = '../api/products.php';
            if (productId) {
                url += `?id=${productId}`;
            } else if (productSlug) {
                url += `?slug=${productSlug}`;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                this.currentProduct = data.data;
                this.displayProduct(data.data);
            } else {
                console.error('Product not found');
                this.loadDefaultProduct();
            }
        } catch (error) {
            console.error('Error loading product:', error);
            this.loadDefaultProduct();
        }
    }

    loadDefaultProduct() {
        // Default product data for demo
        this.currentProduct = {
            id: 1,
            name: 'Premium Anime Figure Collection',
            price: 89.99,
            sale_price: 119.99,
            description: 'Experience the ultimate anime collection with this premium figure set. Crafted with exceptional attention to detail, each piece captures the essence of your favorite characters. Perfect for collectors and anime enthusiasts alike.',
            images: [
                'images/product1.jpg',
                'images/product2.jpg',
                'images/product3.jpg'
            ],
            stock_quantity: 15,
            sku: 'ANM-001',
            category: 'Figures',
            tags: ['Anime', 'Collectible', 'Figure'],
            rating: 4.5,
            review_count: 128
        };

        this.displayProduct(this.currentProduct);
    }

    displayProduct(product) {
        // Update page title
        document.title = `${product.name} - Infinite Shadow`;

        // Update product name
        const nameElement = document.getElementById('productName');
        if (nameElement) {
            nameElement.textContent = product.name;
        }

        // Update prices
        this.updatePriceDisplay(product);

        // Update description
        const descElement = document.querySelector('.product-description p');
        if (descElement && product.description) {
            descElement.textContent = product.description;
        }

        // Update images
        this.updateProductImages(product);

        // Update meta information
        this.updateProductMeta(product);

        // Update rating
        this.updateRating(product);

        // Update stock status
        this.updateStockStatus(product);
    }

    updatePriceDisplay(product) {
        const currentPriceElement = document.getElementById('currentPrice');
        const originalPriceElement = document.getElementById('originalPrice');
        const discountBadge = document.querySelector('.discount-badge');

        if (currentPriceElement) {
            const displayPrice = product.sale_price || product.price;
            currentPriceElement.textContent = `$${parseFloat(displayPrice).toFixed(2)}`;
        }

        if (product.sale_price && originalPriceElement) {
            originalPriceElement.textContent = `$${parseFloat(product.price).toFixed(2)}`;
            originalPriceElement.style.display = 'inline';
            
            if (discountBadge) {
                const discount = Math.round(((product.price - product.sale_price) / product.price) * 100);
                discountBadge.textContent = `${discount}% OFF`;
                discountBadge.style.display = 'inline';
            }
        } else {
            if (originalPriceElement) originalPriceElement.style.display = 'none';
            if (discountBadge) discountBadge.style.display = 'none';
        }
    }

    updateProductImages(product) {
        const mainImage = document.getElementById('mainImage');
        const thumbnailsContainer = document.getElementById('thumbnails');

        if (product.images && product.images.length > 0) {
            // Update main image
            if (mainImage) {
                mainImage.src = product.images[0];
                mainImage.alt = product.name;
            }

            // Update thumbnails
            if (thumbnailsContainer) {
                thumbnailsContainer.innerHTML = '';
                product.images.forEach((image, index) => {
                    const thumbnail = document.createElement('img');
                    thumbnail.src = image;
                    thumbnail.alt = `${product.name} - Image ${index + 1}`;
                    thumbnail.className = `thumbnail ${index === 0 ? 'active' : ''}`;
                    thumbnail.onclick = () => this.changeMainImage(thumbnail);
                    thumbnailsContainer.appendChild(thumbnail);
                });
            }
        }
    }

    updateProductMeta(product) {
        const metaItems = document.querySelectorAll('.meta-item');
        
        metaItems.forEach(item => {
            const label = item.querySelector('span:first-child').textContent.toLowerCase();
            const valueSpan = item.querySelector('span:last-child');
            
            switch (label) {
                case 'sku:':
                    if (product.sku) valueSpan.textContent = product.sku;
                    break;
                case 'category:':
                    if (product.category) valueSpan.textContent = product.category;
                    break;
                case 'tags:':
                    if (product.tags) {
                        valueSpan.textContent = Array.isArray(product.tags) ? 
                            product.tags.join(', ') : product.tags;
                    }
                    break;
            }
        });
    }

    updateRating(product) {
        if (product.rating && product.review_count) {
            const ratingText = document.querySelector('.product-rating span');
            if (ratingText) {
                ratingText.textContent = `(${product.rating}) ${product.review_count} reviews`;
            }

            // Update stars
            const starsContainer = document.querySelector('.stars');
            if (starsContainer) {
                this.displayStars(starsContainer, product.rating);
            }
        }
    }

    displayStars(container, rating) {
        container.innerHTML = '';
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            container.innerHTML += '<i class="fas fa-star"></i>';
        }

        if (hasHalfStar) {
            container.innerHTML += '<i class="fas fa-star-half-alt"></i>';
        }

        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        for (let i = 0; i < emptyStars; i++) {
            container.innerHTML += '<i class="far fa-star"></i>';
        }
    }

    updateStockStatus(product) {
        const stockElement = document.querySelector('.meta-item:last-child span:last-child');
        if (stockElement && product.stock_quantity !== undefined) {
            if (product.stock_quantity > 0) {
                stockElement.textContent = 'In Stock';
                stockElement.className = 'in-stock';
                stockElement.style.color = '#48c774';
            } else {
                stockElement.textContent = 'Out of Stock';
                stockElement.className = 'out-of-stock';
                stockElement.style.color = '#ff6b6b';
            }
        }
    }

    changeMainImage(thumbnail) {
        const mainImage = document.getElementById('mainImage');
        if (mainImage && thumbnail) {
            mainImage.src = thumbnail.src;
            
            // Update active thumbnail
            document.querySelectorAll('.thumbnail').forEach(thumb => {
                thumb.classList.remove('active');
            });
            thumbnail.classList.add('active');
        }
    }

    increaseQuantity() {
        const maxQuantity = this.currentProduct?.stock_quantity || 10;
        if (this.quantity < maxQuantity) {
            this.quantity++;
            this.updateQuantityDisplay();
        }
    }

    decreaseQuantity() {
        if (this.quantity > 1) {
            this.quantity--;
            this.updateQuantityDisplay();
        }
    }

    updateQuantityDisplay() {
        const quantityInput = document.getElementById('quantity');
        if (quantityInput) {
            quantityInput.value = this.quantity;
        }
    }

    updatePrice() {
        // Update price based on selected options (size, etc.)
        // This is a placeholder for dynamic pricing
        if (this.currentProduct) {
            this.updatePriceDisplay(this.currentProduct);
        }
    }

    async addToCart() {
        if (!this.currentProduct) {
            this.showNotification('Product not loaded', 'error');
            return;
        }

        const size = document.getElementById('size')?.value;
        
        try {
            const response = await fetch('../api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'add',
                    product_id: this.currentProduct.id,
                    quantity: this.quantity,
                    options: { size }
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Product added to cart!', 'success');
                this.updateCartCount(data.cart_count);
            } else {
                this.showNotification(data.message || 'Failed to add to cart', 'error');
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            this.showNotification('Error adding to cart', 'error');
        }
    }

    addToWishlist() {
        // Placeholder for wishlist functionality
        this.showNotification('Added to wishlist!', 'success');
    }

    updateCartCount(count) {
        const cartCountElement = document.querySelector('.cart-count');
        if (cartCountElement) {
            cartCountElement.textContent = count || 0;
        }
    }

    async loadRelatedProducts() {
        try {
            const categoryId = this.currentProduct?.category_id;
            let url = '../api/products.php?limit=4';
            
            if (categoryId) {
                url += `&category_id=${categoryId}`;
            }

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                this.displayRelatedProducts(data.data);
            }
        } catch (error) {
            console.error('Error loading related products:', error);
        }
    }

    displayRelatedProducts(products) {
        const container = document.getElementById('relatedProducts');
        if (!container) return;

        container.innerHTML = '';

        products.forEach(product => {
            if (product.id !== this.currentProduct?.id) {
                const productCard = this.createProductCard(product);
                container.appendChild(productCard);
            }
        });
    }

    createProductCard(product) {
        const card = document.createElement('div');
        card.className = 'product-card';
        
        const price = product.sale_price || product.price;
        const originalPrice = product.sale_price ? product.price : null;
        
        card.innerHTML = `
            <div class="product-image">
                <img src="${product.primary_image || 'images/placeholder.jpg'}" alt="${product.name}">
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <div class="product-price">
                    <span class="current-price">$${parseFloat(price).toFixed(2)}</span>
                    ${originalPrice ? `<span class="original-price">$${parseFloat(originalPrice).toFixed(2)}</span>` : ''}
                </div>
                <div class="product-actions">
                    <button class="btn" onclick="window.location.href='product-detail.html?id=${product.id}'">
                        View Details
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    showNotification(message, type = 'info') {
        // Use the social sharing notification system if available
        if (window.socialSharing) {
            window.socialSharing.showNotification(message, type);
        } else {
            // Fallback to alert
            alert(message);
        }
    }
}

// Initialize product detail page
document.addEventListener('DOMContentLoaded', () => {
    new ProductDetail();
});
