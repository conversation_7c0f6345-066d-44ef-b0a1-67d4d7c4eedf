// Responsive Design JavaScript

class ResponsiveManager {
    constructor() {
        this.breakpoints = {
            mobile: 480,
            tablet: 768,
            desktop: 1024,
            large: 1200
        };
        this.currentBreakpoint = this.getCurrentBreakpoint();
        this.init();
    }

    init() {
        this.bindEvents();
        this.handleResize();
        this.initMobileMenu();
        this.initMobileFilters();
        this.optimizeImages();
    }

    bindEvents() {
        // Window resize handler
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Orientation change handler
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleResize();
            }, 100);
        });

        // Touch events for mobile interactions
        this.initTouchEvents();
    }

    getCurrentBreakpoint() {
        const width = window.innerWidth;
        
        if (width <= this.breakpoints.mobile) return 'mobile';
        if (width <= this.breakpoints.tablet) return 'tablet';
        if (width <= this.breakpoints.desktop) return 'desktop';
        return 'large';
    }

    handleResize() {
        const newBreakpoint = this.getCurrentBreakpoint();
        
        if (newBreakpoint !== this.currentBreakpoint) {
            this.currentBreakpoint = newBreakpoint;
            this.onBreakpointChange(newBreakpoint);
        }

        this.adjustLayout();
        this.optimizeImages();
    }

    onBreakpointChange(breakpoint) {
        // Emit custom event for breakpoint change
        window.dispatchEvent(new CustomEvent('breakpointChange', {
            detail: { breakpoint, width: window.innerWidth }
        }));

        // Handle specific breakpoint changes
        switch (breakpoint) {
            case 'mobile':
                this.enableMobileOptimizations();
                break;
            case 'tablet':
                this.enableTabletOptimizations();
                break;
            case 'desktop':
            case 'large':
                this.enableDesktopOptimizations();
                break;
        }
    }

    enableMobileOptimizations() {
        // Close any open dropdowns or modals
        this.closeMobileMenus();
        
        // Optimize touch targets
        this.optimizeTouchTargets();
        
        // Enable swipe gestures
        this.enableSwipeGestures();
    }

    enableTabletOptimizations() {
        // Adjust grid layouts for tablet
        this.adjustGridLayouts('tablet');
        
        // Optimize touch interactions
        this.optimizeTouchTargets();
    }

    enableDesktopOptimizations() {
        // Disable mobile-specific features
        this.disableSwipeGestures();
        
        // Restore desktop layouts
        this.adjustGridLayouts('desktop');
    }

    initMobileMenu() {
        const mobileMenuBtn = document.querySelector('.mobile-menu');
        const navLinks = document.querySelector('.nav-links');
        
        if (mobileMenuBtn && navLinks) {
            mobileMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                navLinks.classList.toggle('active');
                mobileMenuBtn.classList.toggle('active');
                
                // Prevent body scroll when menu is open
                if (navLinks.classList.contains('active')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!navLinks.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                    navLinks.classList.remove('active');
                    mobileMenuBtn.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });

            // Close menu when clicking on a link
            navLinks.addEventListener('click', (e) => {
                if (e.target.tagName === 'A') {
                    navLinks.classList.remove('active');
                    mobileMenuBtn.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        }
    }

    initMobileFilters() {
        const filterToggle = document.getElementById('filterToggle');
        const filtersRow = document.querySelector('.filters-row');
        
        if (filterToggle && filtersRow) {
            filterToggle.addEventListener('click', () => {
                filtersRow.classList.toggle('mobile-hidden');
                
                const icon = filterToggle.querySelector('i');
                if (filtersRow.classList.contains('mobile-hidden')) {
                    icon.className = 'fas fa-filter';
                    filterToggle.innerHTML = '<i class="fas fa-filter"></i> Show Filters';
                } else {
                    icon.className = 'fas fa-times';
                    filterToggle.innerHTML = '<i class="fas fa-times"></i> Hide Filters';
                }
            });
        }
    }

    initTouchEvents() {
        // Add touch event support for better mobile interaction
        let touchStartX = 0;
        let touchStartY = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            if (!e.changedTouches.length) return;
            
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            
            // Detect swipe gestures
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    this.handleSwipeRight();
                } else {
                    this.handleSwipeLeft();
                }
            }
        }, { passive: true });
    }

    handleSwipeRight() {
        // Handle right swipe (e.g., open mobile menu)
        if (this.currentBreakpoint === 'mobile') {
            const navLinks = document.querySelector('.nav-links');
            if (navLinks && !navLinks.classList.contains('active')) {
                navLinks.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }
    }

    handleSwipeLeft() {
        // Handle left swipe (e.g., close mobile menu)
        if (this.currentBreakpoint === 'mobile') {
            const navLinks = document.querySelector('.nav-links');
            if (navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                document.body.style.overflow = '';
            }
        }
    }

    enableSwipeGestures() {
        // Enable swipe gestures for mobile
        document.body.classList.add('swipe-enabled');
    }

    disableSwipeGestures() {
        // Disable swipe gestures for desktop
        document.body.classList.remove('swipe-enabled');
    }

    optimizeTouchTargets() {
        // Ensure touch targets are at least 44px for mobile
        const buttons = document.querySelectorAll('button, .btn, a');
        
        buttons.forEach(button => {
            if (this.currentBreakpoint === 'mobile') {
                const rect = button.getBoundingClientRect();
                if (rect.height < 44) {
                    button.style.minHeight = '44px';
                    button.style.display = 'flex';
                    button.style.alignItems = 'center';
                    button.style.justifyContent = 'center';
                }
            } else {
                button.style.minHeight = '';
            }
        });
    }

    adjustGridLayouts(breakpoint) {
        const grids = document.querySelectorAll('.products-grid, .stats-grid, .charts-grid');
        
        grids.forEach(grid => {
            grid.classList.remove('mobile-layout', 'tablet-layout', 'desktop-layout');
            grid.classList.add(`${breakpoint}-layout`);
        });
    }

    optimizeImages() {
        const images = document.querySelectorAll('img[data-src]');
        
        images.forEach(img => {
            if (this.isInViewport(img)) {
                this.loadImage(img);
            }
        });
    }

    loadImage(img) {
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        }
    }

    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    adjustLayout() {
        // Adjust specific layout elements based on screen size
        this.adjustModals();
        this.adjustTables();
        this.adjustCharts();
    }

    adjustModals() {
        const modals = document.querySelectorAll('.modal-content');
        
        modals.forEach(modal => {
            if (this.currentBreakpoint === 'mobile') {
                modal.style.width = '95%';
                modal.style.height = '90vh';
                modal.style.margin = '5% auto';
            } else {
                modal.style.width = '';
                modal.style.height = '';
                modal.style.margin = '';
            }
        });
    }

    adjustTables() {
        const tables = document.querySelectorAll('table');
        
        tables.forEach(table => {
            const wrapper = table.closest('.table-wrapper') || table.parentNode;
            
            if (this.currentBreakpoint === 'mobile') {
                wrapper.style.overflowX = 'auto';
                table.style.minWidth = '600px';
            } else {
                wrapper.style.overflowX = '';
                table.style.minWidth = '';
            }
        });
    }

    adjustCharts() {
        // Trigger chart resize if Chart.js is available
        if (window.Chart) {
            Object.values(window.Chart.instances || {}).forEach(chart => {
                if (chart && chart.resize) {
                    chart.resize();
                }
            });
        }
    }

    closeMobileMenus() {
        // Close mobile navigation
        const navLinks = document.querySelector('.nav-links');
        const mobileMenuBtn = document.querySelector('.mobile-menu');
        
        if (navLinks && navLinks.classList.contains('active')) {
            navLinks.classList.remove('active');
            if (mobileMenuBtn) mobileMenuBtn.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Close any open dropdowns
        const dropdowns = document.querySelectorAll('.dropdown.active');
        dropdowns.forEach(dropdown => {
            dropdown.classList.remove('active');
        });
    }

    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Public methods for external use
    isMobile() {
        return this.currentBreakpoint === 'mobile';
    }

    isTablet() {
        return this.currentBreakpoint === 'tablet';
    }

    isDesktop() {
        return this.currentBreakpoint === 'desktop' || this.currentBreakpoint === 'large';
    }

    getCurrentBreakpointName() {
        return this.currentBreakpoint;
    }
}

// Initialize responsive manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.responsiveManager = new ResponsiveManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ResponsiveManager;
}
