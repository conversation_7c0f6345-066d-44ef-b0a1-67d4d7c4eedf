document.addEventListener('DOMContentLoaded', function() {
    // Theme Toggle Functionality
    const themeToggle = document.querySelector('.theme-toggle');
    const body = document.body;
    const themeIcon = document.querySelector('.theme-toggle i');

    // Check for saved theme preference or use default
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark-mode') {
        body.classList.add('dark-mode');
    } else if (savedTheme === '') {
        body.classList.remove('dark-mode');
    }
    // If no saved theme, keep the default from HTML

    // Update icon based on current theme
    updateThemeIcon();

    // Toggle theme when clicked
    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            if (body.classList.contains('dark-mode')) {
                body.classList.remove('dark-mode');
                localStorage.setItem('theme', '');
            } else {
                body.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark-mode');
            }
            updateThemeIcon();
        });
    }

    // Update theme icon based on current theme
    function updateThemeIcon() {
        if (themeIcon) {
            if (body.classList.contains('dark-mode')) {
                themeIcon.className = 'fas fa-sun';
            } else {
                themeIcon.className = 'fas fa-moon';
            }
        }
    }
    
    // Mobile Menu Toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu');
    const navLinks = document.querySelector('.nav-links');

    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', () => {
            navLinks.classList.toggle('active');
        });
    }
    
    // Shopping Cart Functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    const cartCount = document.querySelector('.cart-count');
    
    // Initialize cart from localStorage or create empty cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    updateCartCount();
    
    // Add to cart functionality
    addToCartButtons.forEach(button => {
        button.addEventListener('click', () => {
            const productId = button.getAttribute('data-id');
            const productCard = button.closest('.product-card');
            const productName = productCard.querySelector('h3').textContent;
            const productPrice = productCard.querySelector('.price').textContent;
            const productImage = productCard.querySelector('img').src;
            
            // Check if product already in cart
            const existingProduct = cart.find(item => item.id === productId);
            
            if (existingProduct) {
                existingProduct.quantity += 1;
            } else {
                cart.push({
                    id: productId,
                    name: productName,
                    price: productPrice,
                    image: productImage,
                    quantity: 1
                });
            }
            
            // Save cart to localStorage
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            
            // Show notification
            showNotification(`${productName} added to cart!`);
        });
    });
    
    // Update cart count
    function updateCartCount() {
        const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
        cartCount.textContent = totalItems;
    }
    
    // Notification function
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Wishlist functionality
    const wishlistButtons = document.querySelectorAll('.add-to-wishlist');
    let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
    
    wishlistButtons.forEach(button => {
        const productId = button.getAttribute('data-id');
        
        // Check if product is in wishlist and update icon
        if (wishlist.includes(productId)) {
            button.querySelector('i').classList.remove('far');
            button.querySelector('i').classList.add('fas');
            button.classList.add('active');
        }
        
        button.addEventListener('click', () => {
            const productId = button.getAttribute('data-id');
            const productCard = button.closest('.product-card');
            const productName = productCard.querySelector('h3').textContent;
            
            if (wishlist.includes(productId)) {
                // Remove from wishlist
                wishlist = wishlist.filter(id => id !== productId);
                button.querySelector('i').classList.remove('fas');
                button.querySelector('i').classList.add('far');
                button.classList.remove('active');
                showNotification(`${productName} removed from wishlist!`);
            } else {
                // Add to wishlist
                wishlist.push(productId);
                button.querySelector('i').classList.remove('far');
                button.querySelector('i').classList.add('fas');
                button.classList.add('active');
                showNotification(`${productName} added to wishlist!`);
            }
            
            localStorage.setItem('wishlist', JSON.stringify(wishlist));
        });
    });
    
    // Newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-form');
    
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const emailInput = this.querySelector('input[type="email"]');
            const email = emailInput.value;
            
            // Here you would typically send this to your server
            console.log('Newsletter subscription for:', email);
            
            // Show success message
            showNotification('Thank you for subscribing to our newsletter!');
            
            // Clear the form
            emailInput.value = '';
        });
    }
});