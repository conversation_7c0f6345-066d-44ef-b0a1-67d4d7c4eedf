// Advanced Search JavaScript

class SearchManager {
    constructor() {
        this.searchTimeout = null;
        this.currentQuery = '';
        this.currentFilters = {};
        this.searchHistory = [];
        this.suggestions = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSearchHistory();
        this.loadPopularSearches();
    }

    bindEvents() {
        // Search input events
        const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
        searchInputs.forEach(input => {
            input.addEventListener('input', (e) => this.handleSearchInput(e));
            input.addEventListener('focus', (e) => this.showSearchSuggestions(e));
            input.addEventListener('blur', (e) => this.hideSearchSuggestions(e));
            input.addEventListener('keydown', (e) => this.handleSearchKeydown(e));
        });

        // Search form submissions
        const searchForms = document.querySelectorAll('.search-form, form[role="search"]');
        searchForms.forEach(form => {
            form.addEventListener('submit', (e) => this.handleSearchSubmit(e));
        });

        // Filter changes
        const filterInputs = document.querySelectorAll('.filter-input, .sort-select');
        filterInputs.forEach(input => {
            input.addEventListener('change', () => this.handleFilterChange());
        });

        // Clear search button
        const clearBtns = document.querySelectorAll('.clear-search');
        clearBtns.forEach(btn => {
            btn.addEventListener('click', () => this.clearSearch());
        });

        // Voice search (if supported)
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            this.initVoiceSearch();
        }
    }

    handleSearchInput(e) {
        const query = e.target.value.trim();
        
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // Debounce search suggestions
        this.searchTimeout = setTimeout(() => {
            if (query.length >= 2) {
                this.getSuggestions(query);
            } else {
                this.hideSuggestions();
            }
        }, 300);
    }

    handleSearchKeydown(e) {
        const suggestionsList = document.querySelector('.search-suggestions');
        if (!suggestionsList || suggestionsList.style.display === 'none') return;

        const suggestions = suggestionsList.querySelectorAll('.suggestion-item');
        let currentIndex = Array.from(suggestions).findIndex(item => item.classList.contains('active'));

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentIndex = currentIndex < suggestions.length - 1 ? currentIndex + 1 : 0;
                this.highlightSuggestion(suggestions, currentIndex);
                break;
            case 'ArrowUp':
                e.preventDefault();
                currentIndex = currentIndex > 0 ? currentIndex - 1 : suggestions.length - 1;
                this.highlightSuggestion(suggestions, currentIndex);
                break;
            case 'Enter':
                if (currentIndex >= 0) {
                    e.preventDefault();
                    suggestions[currentIndex].click();
                }
                break;
            case 'Escape':
                this.hideSuggestions();
                e.target.blur();
                break;
        }
    }

    handleSearchSubmit(e) {
        e.preventDefault();
        const form = e.target;
        const searchInput = form.querySelector('input[type="search"], .search-input');
        
        if (searchInput) {
            const query = searchInput.value.trim();
            if (query) {
                this.performSearch(query);
            }
        }
    }

    handleFilterChange() {
        const filters = this.collectFilters();
        this.currentFilters = filters;
        
        if (this.currentQuery) {
            this.performSearch(this.currentQuery, filters);
        }
    }

    async performSearch(query, filters = {}) {
        this.currentQuery = query;
        this.currentFilters = filters;
        
        // Add to search history
        this.addToSearchHistory(query);
        
        // Hide suggestions
        this.hideSuggestions();
        
        // Show loading state
        this.showSearchLoading();
        
        try {
            const params = new URLSearchParams({
                q: query,
                ...filters,
                limit: 20,
                offset: 0
            });
            
            const response = await fetch(`api/search.php?action=search&${params.toString()}`);
            const data = await response.json();
            
            if (data.success) {
                this.displaySearchResults(data.data);
                this.updateSearchStats(data.data);
            } else {
                this.showSearchError(data.message);
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showSearchError('Search failed. Please try again.');
        } finally {
            this.hideSearchLoading();
        }
    }

    async getSuggestions(query) {
        try {
            const response = await fetch(`api/search.php?action=suggestions&q=${encodeURIComponent(query)}&limit=8`);
            const data = await response.json();
            
            if (data.success) {
                this.suggestions = data.data;
                this.displaySuggestions(data.data);
            }
        } catch (error) {
            console.error('Error getting suggestions:', error);
        }
    }

    displaySuggestions(suggestions) {
        let suggestionsList = document.querySelector('.search-suggestions');
        
        if (!suggestionsList) {
            suggestionsList = this.createSuggestionsContainer();
        }
        
        suggestionsList.innerHTML = '';
        
        if (suggestions.length === 0) {
            suggestionsList.style.display = 'none';
            return;
        }
        
        suggestions.forEach((suggestion, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.dataset.type = suggestion.type;
            
            const icon = this.getSuggestionIcon(suggestion.type);
            item.innerHTML = `
                <i class="${icon}"></i>
                <span class="suggestion-text">${this.highlightQuery(suggestion.suggestion)}</span>
                <span class="suggestion-type">${suggestion.type}</span>
            `;
            
            item.addEventListener('click', () => {
                this.selectSuggestion(suggestion.suggestion);
            });
            
            suggestionsList.appendChild(item);
        });
        
        // Add search history if available
        if (this.searchHistory.length > 0) {
            const historySection = document.createElement('div');
            historySection.className = 'suggestions-section';
            historySection.innerHTML = '<div class="section-title">Recent Searches</div>';
            
            this.searchHistory.slice(0, 3).forEach(historyItem => {
                const item = document.createElement('div');
                item.className = 'suggestion-item history-item';
                item.innerHTML = `
                    <i class="fas fa-history"></i>
                    <span class="suggestion-text">${historyItem.query}</span>
                    <button class="remove-history" data-query="${historyItem.query}">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                item.addEventListener('click', (e) => {
                    if (!e.target.closest('.remove-history')) {
                        this.selectSuggestion(historyItem.query);
                    }
                });
                
                const removeBtn = item.querySelector('.remove-history');
                removeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.removeFromSearchHistory(historyItem.query);
                });
                
                historySection.appendChild(item);
            });
            
            suggestionsList.appendChild(historySection);
        }
        
        suggestionsList.style.display = 'block';
    }

    createSuggestionsContainer() {
        const container = document.createElement('div');
        container.className = 'search-suggestions';
        
        // Find the search input to position suggestions
        const searchInput = document.querySelector('input[type="search"], .search-input');
        if (searchInput) {
            const parent = searchInput.closest('.search-box, .search-container') || searchInput.parentNode;
            parent.style.position = 'relative';
            parent.appendChild(container);
        }
        
        return container;
    }

    getSuggestionIcon(type) {
        switch (type) {
            case 'product': return 'fas fa-box';
            case 'category': return 'fas fa-tags';
            case 'tag': return 'fas fa-hashtag';
            default: return 'fas fa-search';
        }
    }

    highlightQuery(text) {
        const query = this.currentQuery || '';
        if (!query) return text;
        
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    selectSuggestion(suggestion) {
        const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
        searchInputs.forEach(input => {
            input.value = suggestion;
        });
        
        this.hideSuggestions();
        this.performSearch(suggestion);
    }

    highlightSuggestion(suggestions, index) {
        suggestions.forEach((item, i) => {
            item.classList.toggle('active', i === index);
        });
    }

    showSearchSuggestions(e) {
        const query = e.target.value.trim();
        if (query.length >= 2) {
            this.getSuggestions(query);
        }
    }

    hideSearchSuggestions(e) {
        // Delay hiding to allow for clicks on suggestions
        setTimeout(() => {
            this.hideSuggestions();
        }, 200);
    }

    hideSuggestions() {
        const suggestionsList = document.querySelector('.search-suggestions');
        if (suggestionsList) {
            suggestionsList.style.display = 'none';
        }
    }

    collectFilters() {
        const filters = {};
        
        // Category filter
        const categorySelect = document.getElementById('category');
        if (categorySelect && categorySelect.value && categorySelect.value !== 'all') {
            filters.category = categorySelect.value;
        }
        
        // Sort filter
        const sortSelect = document.getElementById('sort');
        if (sortSelect && sortSelect.value) {
            filters.sort = sortSelect.value;
        }
        
        // Price filters
        const minPrice = document.getElementById('minPrice');
        const maxPrice = document.getElementById('maxPrice');
        if (minPrice && minPrice.value) {
            filters.min_price = minPrice.value;
        }
        if (maxPrice && maxPrice.value) {
            filters.max_price = maxPrice.value;
        }
        
        // Rating filter
        const minRating = document.getElementById('minRating');
        if (minRating && minRating.value) {
            filters.min_rating = minRating.value;
        }
        
        // Checkbox filters
        const inStockCheckbox = document.getElementById('inStock');
        if (inStockCheckbox && inStockCheckbox.checked) {
            filters.in_stock = true;
        }
        
        const onSaleCheckbox = document.getElementById('onSale');
        if (onSaleCheckbox && onSaleCheckbox.checked) {
            filters.on_sale = true;
        }
        
        return filters;
    }

    displaySearchResults(data) {
        const resultsContainer = document.getElementById('searchResults') || document.querySelector('.products-grid');
        
        if (!resultsContainer) {
            console.error('Search results container not found');
            return;
        }
        
        // Update results count
        this.updateResultsCount(data.total, data.query);
        
        // Clear existing results
        resultsContainer.innerHTML = '';
        
        if (data.products.length === 0) {
            this.showNoResults(data.query);
            return;
        }
        
        // Display products
        data.products.forEach(product => {
            const productElement = this.createProductElement(product);
            resultsContainer.appendChild(productElement);
        });
        
        // Update pagination if needed
        this.updatePagination(data);
    }

    createProductElement(product) {
        const div = document.createElement('div');
        div.className = 'product-card';
        
        const price = product.sale_price || product.price;
        const originalPrice = product.sale_price ? product.price : null;
        
        div.innerHTML = `
            <div class="product-image">
                <img src="${product.primary_image || 'images/placeholder.jpg'}" alt="${product.name}">
                <div class="product-actions">
                    <button class="action-btn wishlist-btn" onclick="addToWishlist(${product.id})">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button class="action-btn quick-view-btn" onclick="quickView(${product.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <div class="product-price">
                    $${parseFloat(price).toFixed(2)}
                    ${originalPrice ? `<span class="original-price">$${parseFloat(originalPrice).toFixed(2)}</span>` : ''}
                </div>
                <div class="product-rating">
                    ${this.generateStarRating(product.average_rating)}
                    <span class="rating-count">(${product.review_count || 0})</span>
                </div>
                <button class="btn add-to-cart-btn" onclick="addToCart(${product.id})">
                    Add to Cart
                </button>
            </div>
        `;
        
        return div;
    }

    generateStarRating(rating) {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        
        for (let i = 0; i < 5; i++) {
            if (i < fullStars) {
                stars.push('<i class="fas fa-star"></i>');
            } else if (i === fullStars && hasHalfStar) {
                stars.push('<i class="fas fa-star-half-alt"></i>');
            } else {
                stars.push('<i class="far fa-star"></i>');
            }
        }
        
        return `<div class="stars">${stars.join('')}</div>`;
    }

    updateResultsCount(total, query) {
        const countElement = document.getElementById('resultsCount');
        if (countElement) {
            countElement.textContent = `${total} results found for "${query}"`;
        }
    }

    showNoResults(query) {
        const resultsContainer = document.getElementById('searchResults') || document.querySelector('.products-grid');
        
        resultsContainer.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No results found</h3>
                <p>We couldn't find any products matching "${query}"</p>
                <div class="no-results-suggestions">
                    <p>Try:</p>
                    <ul>
                        <li>Checking your spelling</li>
                        <li>Using different keywords</li>
                        <li>Browsing our categories</li>
                    </ul>
                </div>
                <a href="shop.html" class="btn">Browse All Products</a>
            </div>
        `;
    }

    showSearchLoading() {
        const resultsContainer = document.getElementById('searchResults') || document.querySelector('.products-grid');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="search-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Searching...</p>
                </div>
            `;
        }
    }

    hideSearchLoading() {
        // Loading will be replaced by results
    }

    showSearchError(message) {
        const resultsContainer = document.getElementById('searchResults') || document.querySelector('.products-grid');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="search-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Search Error</h3>
                    <p>${message}</p>
                    <button class="btn" onclick="location.reload()">Try Again</button>
                </div>
            `;
        }
    }

    addToSearchHistory(query) {
        // Remove if already exists
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);
        
        // Add to beginning
        this.searchHistory.unshift({
            query: query,
            timestamp: new Date().toISOString()
        });
        
        // Keep only last 10 searches
        this.searchHistory = this.searchHistory.slice(0, 10);
        
        // Save to localStorage
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    removeFromSearchHistory(query) {
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    loadSearchHistory() {
        try {
            const stored = localStorage.getItem('searchHistory');
            if (stored) {
                this.searchHistory = JSON.parse(stored);
            }
        } catch (error) {
            console.error('Error loading search history:', error);
            this.searchHistory = [];
        }
    }

    async loadPopularSearches() {
        try {
            const response = await fetch('api/search.php?action=popular&limit=5');
            const data = await response.json();
            
            if (data.success) {
                this.displayPopularSearches(data.data);
            }
        } catch (error) {
            console.error('Error loading popular searches:', error);
        }
    }

    displayPopularSearches(searches) {
        const container = document.getElementById('popularSearches');
        if (!container) return;
        
        container.innerHTML = '';
        
        searches.forEach(search => {
            const tag = document.createElement('span');
            tag.className = 'popular-search-tag';
            tag.textContent = search.query;
            tag.addEventListener('click', () => {
                this.selectSuggestion(search.query);
            });
            container.appendChild(tag);
        });
    }

    clearSearch() {
        const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
        searchInputs.forEach(input => {
            input.value = '';
        });
        
        this.hideSuggestions();
        this.currentQuery = '';
        
        // Clear results if on search page
        const resultsContainer = document.getElementById('searchResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
        }
    }

    initVoiceSearch() {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (!voiceBtn) return;
        
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';
        
        voiceBtn.addEventListener('click', () => {
            recognition.start();
            voiceBtn.classList.add('listening');
        });
        
        recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
            searchInputs.forEach(input => {
                input.value = transcript;
            });
            this.performSearch(transcript);
        };
        
        recognition.onend = () => {
            voiceBtn.classList.remove('listening');
        };
        
        recognition.onerror = () => {
            voiceBtn.classList.remove('listening');
        };
    }

    updateSearchStats(data) {
        // Update any search statistics displays
        const statsElement = document.getElementById('searchStats');
        if (statsElement) {
            statsElement.innerHTML = `
                <span>Found ${data.total} results in ${data.products.length} products</span>
            `;
        }
    }

    updatePagination(data) {
        // Implement pagination if needed
        const paginationContainer = document.getElementById('searchPagination');
        if (!paginationContainer) return;
        
        const totalPages = Math.ceil(data.total / data.limit);
        const currentPage = Math.floor(data.offset / data.limit) + 1;
        
        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }
        
        paginationContainer.style.display = 'block';
        // Implement pagination controls here
    }
}

// Initialize search manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.searchManager = new SearchManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchManager;
}
