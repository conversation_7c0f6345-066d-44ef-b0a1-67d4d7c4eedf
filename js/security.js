// Security and Performance JavaScript

class SecurityManager {
    constructor() {
        this.csrfToken = null;
        this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
        this.warningTime = 5 * 60 * 1000; // 5 minutes before timeout
        this.sessionTimer = null;
        this.warningTimer = null;
        this.init();
    }

    init() {
        this.loadCSRFToken();
        this.initSessionManagement();
        this.initSecurityHeaders();
        this.initFormValidation();
        this.initPasswordStrength();
        this.preventClickjacking();
        this.initContentSecurityPolicy();
    }

    loadCSRFToken() {
        // Get CSRF token from meta tag or API
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            this.csrfToken = metaToken.getAttribute('content');
        } else {
            this.fetchCSRFToken();
        }
    }

    async fetchCSRFToken() {
        try {
            const response = await fetch('/api/csrf-token.php');
            const data = await response.json();
            if (data.success) {
                this.csrfToken = data.token;
                this.updateCSRFTokenInForms();
            }
        } catch (error) {
            console.error('Failed to fetch CSRF token:', error);
        }
    }

    updateCSRFTokenInForms() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            let tokenInput = form.querySelector('input[name="csrf_token"]');
            if (!tokenInput) {
                tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = 'csrf_token';
                form.appendChild(tokenInput);
            }
            tokenInput.value = this.csrfToken;
        });
    }

    initSessionManagement() {
        this.resetSessionTimer();
        
        // Reset timer on user activity
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        events.forEach(event => {
            document.addEventListener(event, () => this.resetSessionTimer(), true);
        });

        // Check session status periodically
        setInterval(() => this.checkSessionStatus(), 60000); // Every minute
    }

    resetSessionTimer() {
        // Clear existing timers
        if (this.sessionTimer) clearTimeout(this.sessionTimer);
        if (this.warningTimer) clearTimeout(this.warningTimer);

        // Set warning timer
        this.warningTimer = setTimeout(() => {
            this.showSessionWarning();
        }, this.sessionTimeout - this.warningTime);

        // Set session timeout timer
        this.sessionTimer = setTimeout(() => {
            this.handleSessionTimeout();
        }, this.sessionTimeout);
    }

    showSessionWarning() {
        const modal = this.createSessionWarningModal();
        document.body.appendChild(modal);
        modal.style.display = 'block';

        // Auto-logout countdown
        let countdown = this.warningTime / 1000;
        const countdownElement = modal.querySelector('.countdown');
        
        const countdownInterval = setInterval(() => {
            countdown--;
            if (countdownElement) {
                countdownElement.textContent = Math.ceil(countdown / 60);
            }
            
            if (countdown <= 0) {
                clearInterval(countdownInterval);
                this.handleSessionTimeout();
            }
        }, 1000);

        // Extend session button
        const extendBtn = modal.querySelector('.extend-session');
        if (extendBtn) {
            extendBtn.addEventListener('click', () => {
                clearInterval(countdownInterval);
                modal.remove();
                this.resetSessionTimer();
                this.extendSession();
            });
        }
    }

    createSessionWarningModal() {
        const modal = document.createElement('div');
        modal.className = 'session-warning-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-clock"></i> Session Expiring</h3>
                </div>
                <div class="modal-body">
                    <p>Your session will expire in <span class="countdown">5</span> minutes due to inactivity.</p>
                    <p>Would you like to extend your session?</p>
                </div>
                <div class="modal-footer">
                    <button class="btn extend-session">Extend Session</button>
                    <button class="btn btn-secondary logout-now">Logout Now</button>
                </div>
            </div>
        `;

        // Logout now button
        const logoutBtn = modal.querySelector('.logout-now');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleSessionTimeout();
            });
        }

        return modal;
    }

    async extendSession() {
        try {
            const response = await fetch('/api/extend-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.csrfToken
                }
            });
            
            const data = await response.json();
            if (!data.success) {
                this.handleSessionTimeout();
            }
        } catch (error) {
            console.error('Failed to extend session:', error);
            this.handleSessionTimeout();
        }
    }

    async checkSessionStatus() {
        try {
            const response = await fetch('/api/session-status.php');
            const data = await response.json();
            
            if (!data.valid) {
                this.handleSessionTimeout();
            }
        } catch (error) {
            console.error('Failed to check session status:', error);
        }
    }

    handleSessionTimeout() {
        // Clear any existing modals
        const existingModals = document.querySelectorAll('.session-warning-modal');
        existingModals.forEach(modal => modal.remove());

        // Show logout message
        this.showLogoutMessage();

        // Redirect to login after delay
        setTimeout(() => {
            window.location.href = '/login.html?reason=timeout';
        }, 3000);
    }

    showLogoutMessage() {
        const message = document.createElement('div');
        message.className = 'logout-message';
        message.innerHTML = `
            <div class="message-content">
                <i class="fas fa-sign-out-alt"></i>
                <h3>Session Expired</h3>
                <p>You have been logged out due to inactivity.</p>
                <p>Redirecting to login page...</p>
            </div>
        `;
        document.body.appendChild(message);
    }

    initSecurityHeaders() {
        // Add security headers via meta tags if not set by server
        this.addMetaTag('X-Content-Type-Options', 'nosniff');
        this.addMetaTag('X-Frame-Options', 'DENY');
        this.addMetaTag('X-XSS-Protection', '1; mode=block');
        this.addMetaTag('Referrer-Policy', 'strict-origin-when-cross-origin');
    }

    addMetaTag(name, content) {
        if (!document.querySelector(`meta[http-equiv="${name}"]`)) {
            const meta = document.createElement('meta');
            meta.setAttribute('http-equiv', name);
            meta.setAttribute('content', content);
            document.head.appendChild(meta);
        }
    }

    initFormValidation() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => this.validateForm(e));
        });
    }

    validateForm(event) {
        const form = event.target;
        
        // Check CSRF token
        const csrfInput = form.querySelector('input[name="csrf_token"]');
        if (csrfInput && csrfInput.value !== this.csrfToken) {
            event.preventDefault();
            this.showSecurityError('Security token mismatch. Please refresh the page.');
            return false;
        }

        // Validate required fields
        const requiredFields = form.querySelectorAll('[required]');
        for (let field of requiredFields) {
            if (!field.value.trim()) {
                event.preventDefault();
                this.highlightField(field, 'This field is required');
                return false;
            }
        }

        // Validate email fields
        const emailFields = form.querySelectorAll('input[type="email"]');
        for (let field of emailFields) {
            if (field.value && !this.isValidEmail(field.value)) {
                event.preventDefault();
                this.highlightField(field, 'Please enter a valid email address');
                return false;
            }
        }

        // Validate password fields
        const passwordFields = form.querySelectorAll('input[type="password"]');
        for (let field of passwordFields) {
            if (field.value && field.hasAttribute('data-validate-strength')) {
                const strength = this.checkPasswordStrength(field.value);
                if (strength.score < 3) {
                    event.preventDefault();
                    this.highlightField(field, 'Password is too weak');
                    return false;
                }
            }
        }

        return true;
    }

    highlightField(field, message) {
        field.classList.add('error');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);

        // Remove error styling after user starts typing
        field.addEventListener('input', () => {
            field.classList.remove('error');
            const errorMsg = field.parentNode.querySelector('.field-error');
            if (errorMsg) {
                errorMsg.remove();
            }
        }, { once: true });

        field.focus();
    }

    initPasswordStrength() {
        const passwordFields = document.querySelectorAll('input[type="password"][data-show-strength]');
        passwordFields.forEach(field => {
            this.addPasswordStrengthIndicator(field);
        });
    }

    addPasswordStrengthIndicator(field) {
        const container = document.createElement('div');
        container.className = 'password-strength-container';
        
        const indicator = document.createElement('div');
        indicator.className = 'password-strength-indicator';
        
        const text = document.createElement('div');
        text.className = 'password-strength-text';
        text.textContent = 'Password strength: ';
        
        container.appendChild(indicator);
        container.appendChild(text);
        field.parentNode.appendChild(container);

        field.addEventListener('input', () => {
            const strength = this.checkPasswordStrength(field.value);
            this.updatePasswordStrengthIndicator(indicator, text, strength);
        });
    }

    checkPasswordStrength(password) {
        let score = 0;
        const feedback = [];

        // Length check
        if (password.length >= 8) score += 1;
        else feedback.push('At least 8 characters');

        // Uppercase check
        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('One uppercase letter');

        // Lowercase check
        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('One lowercase letter');

        // Number check
        if (/\d/.test(password)) score += 1;
        else feedback.push('One number');

        // Special character check
        if (/[^a-zA-Z\d]/.test(password)) score += 1;
        else feedback.push('One special character');

        let strength = 'Very Weak';
        if (score >= 5) strength = 'Very Strong';
        else if (score >= 4) strength = 'Strong';
        else if (score >= 3) strength = 'Medium';
        else if (score >= 2) strength = 'Weak';

        return { score, strength, feedback };
    }

    updatePasswordStrengthIndicator(indicator, text, strength) {
        indicator.className = 'password-strength-indicator';
        indicator.classList.add(strength.strength.toLowerCase().replace(' ', '-'));
        
        const percentage = (strength.score / 5) * 100;
        indicator.style.width = percentage + '%';
        
        text.innerHTML = `Password strength: <span class="${strength.strength.toLowerCase().replace(' ', '-')}">${strength.strength}</span>`;
        
        if (strength.feedback.length > 0) {
            text.innerHTML += `<br><small>Missing: ${strength.feedback.join(', ')}</small>`;
        }
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    preventClickjacking() {
        // Prevent the page from being embedded in frames
        if (window.top !== window.self) {
            window.top.location = window.self.location;
        }
    }

    initContentSecurityPolicy() {
        // Report CSP violations
        document.addEventListener('securitypolicyviolation', (e) => {
            console.warn('CSP Violation:', e.violatedDirective, e.blockedURI);
            this.reportSecurityViolation('csp', {
                violatedDirective: e.violatedDirective,
                blockedURI: e.blockedURI,
                sourceFile: e.sourceFile,
                lineNumber: e.lineNumber
            });
        });
    }

    async reportSecurityViolation(type, details) {
        try {
            await fetch('/api/security-report.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.csrfToken
                },
                body: JSON.stringify({
                    type: type,
                    details: details,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                })
            });
        } catch (error) {
            console.error('Failed to report security violation:', error);
        }
    }

    showSecurityError(message) {
        const alert = document.createElement('div');
        alert.className = 'security-alert';
        alert.innerHTML = `
            <div class="alert-content">
                <i class="fas fa-shield-alt"></i>
                <span>${message}</span>
                <button class="close-alert">&times;</button>
            </div>
        `;

        document.body.appendChild(alert);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);

        // Manual close
        const closeBtn = alert.querySelector('.close-alert');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => alert.remove());
        }
    }

    // Sanitize user input
    sanitizeInput(input) {
        const div = document.createElement('div');
        div.textContent = input;
        return div.innerHTML;
    }

    // Validate URLs
    isValidURL(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    // Secure local storage
    secureSetItem(key, value) {
        try {
            const encrypted = btoa(JSON.stringify(value));
            localStorage.setItem(key, encrypted);
            return true;
        } catch (error) {
            console.error('Failed to store item securely:', error);
            return false;
        }
    }

    secureGetItem(key) {
        try {
            const encrypted = localStorage.getItem(key);
            if (!encrypted) return null;
            return JSON.parse(atob(encrypted));
        } catch (error) {
            console.error('Failed to retrieve item securely:', error);
            return null;
        }
    }

    // Rate limiting for API calls
    rateLimitCheck(endpoint, maxCalls = 10, timeWindow = 60000) {
        const key = `rate_limit_${endpoint}`;
        const now = Date.now();
        
        let calls = this.secureGetItem(key) || [];
        
        // Remove old calls outside time window
        calls = calls.filter(timestamp => now - timestamp < timeWindow);
        
        if (calls.length >= maxCalls) {
            return false;
        }
        
        calls.push(now);
        this.secureSetItem(key, calls);
        return true;
    }

    // Initialize security monitoring
    initSecurityMonitoring() {
        // Monitor for suspicious activity
        let rapidClicks = 0;
        let lastClickTime = 0;

        document.addEventListener('click', () => {
            const now = Date.now();
            if (now - lastClickTime < 100) {
                rapidClicks++;
                if (rapidClicks > 10) {
                    this.reportSecurityViolation('rapid_clicking', {
                        clicks: rapidClicks,
                        timespan: now - lastClickTime
                    });
                }
            } else {
                rapidClicks = 0;
            }
            lastClickTime = now;
        });

        // Monitor for console access
        let devtools = false;
        setInterval(() => {
            if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
                if (!devtools) {
                    devtools = true;
                    this.reportSecurityViolation('devtools_opened', {});
                }
            } else {
                devtools = false;
            }
        }, 1000);
    }
}

// Performance monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }

    init() {
        this.monitorPageLoad();
        this.monitorUserInteractions();
        this.monitorResourceLoading();
        this.monitorMemoryUsage();
    }

    monitorPageLoad() {
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            
            this.metrics.pageLoad = {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                totalTime: navigation.loadEventEnd - navigation.fetchStart
            };

            this.reportMetrics('page_load', this.metrics.pageLoad);
        });
    }

    monitorUserInteractions() {
        // Monitor click response times
        document.addEventListener('click', (e) => {
            const startTime = performance.now();
            
            requestAnimationFrame(() => {
                const responseTime = performance.now() - startTime;
                this.recordMetric('click_response_time', responseTime);
            });
        });
    }

    monitorResourceLoading() {
        // Monitor slow loading resources
        const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (entry.duration > 1000) { // Slow resource (>1s)
                    this.reportMetrics('slow_resource', {
                        name: entry.name,
                        duration: entry.duration,
                        type: entry.initiatorType
                    });
                }
            }
        });
        
        observer.observe({ entryTypes: ['resource'] });
    }

    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.metrics.memory = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit
                };

                // Alert if memory usage is high
                const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                if (usagePercent > 80) {
                    this.reportMetrics('high_memory_usage', this.metrics.memory);
                }
            }, 30000); // Check every 30 seconds
        }
    }

    recordMetric(name, value) {
        if (!this.metrics[name]) {
            this.metrics[name] = [];
        }
        
        this.metrics[name].push({
            value: value,
            timestamp: Date.now()
        });

        // Keep only last 100 entries
        if (this.metrics[name].length > 100) {
            this.metrics[name] = this.metrics[name].slice(-100);
        }
    }

    async reportMetrics(type, data) {
        try {
            await fetch('/api/performance-metrics.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: type,
                    data: data,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                })
            });
        } catch (error) {
            console.error('Failed to report performance metrics:', error);
        }
    }

    getMetrics() {
        return this.metrics;
    }
}

// Initialize security and performance monitoring
document.addEventListener('DOMContentLoaded', () => {
    window.securityManager = new SecurityManager();
    window.performanceMonitor = new PerformanceMonitor();
    
    // Initialize security monitoring
    window.securityManager.initSecurityMonitoring();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SecurityManager, PerformanceMonitor };
}
