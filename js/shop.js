document.addEventListener('DOMContentLoaded', function() {
    // Get all products and filter elements
    const products = document.querySelectorAll('.product-card');
    const categoryFilter = document.getElementById('category');
    const sortFilter = document.getElementById('sort');
    const searchInput = document.querySelector('.search-box input');
    const searchButton = document.querySelector('.search-box button');
    
    // Filter products by category
    categoryFilter.addEventListener('change', filterProducts);
    
    // Sort products
    sortFilter.addEventListener('change', filterProducts);
    
    // Search products
    searchButton.addEventListener('click', filterProducts);
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            filterProducts();
        }
    });
    
    // Filter and sort products function
    function filterProducts() {
        const category = categoryFilter.value;
        const sort = sortFilter.value;
        const searchTerm = searchInput.value.toLowerCase().trim();
        
        // Show/hide products based on category and search term
        products.forEach(product => {
            const productCategory = product.getAttribute('data-category');
            const productName = product.querySelector('h3').textContent.toLowerCase();
            
            // Check if product matches category filter
            const categoryMatch = category === 'all' || productCategory === category;
            
            // Check if product matches search term
            const searchMatch = searchTerm === '' || productName.includes(searchTerm);
            
            // Show/hide product
            if (categoryMatch && searchMatch) {
                product.style.display = 'block';
            } else {
                product.style.display = 'none';
            }
        });
        
        // Sort visible products
        const visibleProducts = Array.from(products).filter(product => product.style.display !== 'none');
        
        switch (sort) {
            case 'price-low':
                sortProductsByPrice(visibleProducts, 'asc');
                break;
            case 'price-high':
                sortProductsByPrice(visibleProducts, 'desc');
                break;
            case 'rating':
                sortProductsByRating(visibleProducts);
                break;
            case 'newest':
                sortProductsByNewest(visibleProducts);
                break;
            default:
                // 'featured' - no sorting needed, products are already in featured order
                break;
        }
        
        // Update product display order
        const productsGrid = document.querySelector('.products-grid');
        visibleProducts.forEach(product => {
            productsGrid.appendChild(product);
        });
    }
    
    // Sort products by price
    function sortProductsByPrice(products, direction) {
        products.sort((a, b) => {
            const priceA = parseFloat(a.querySelector('.price').textContent.replace('$', ''));
            const priceB = parseFloat(b.querySelector('.price').textContent.replace('$', ''));
            
            return direction === 'asc' ? priceA - priceB : priceB - priceA;
        });
    }
    
    // Sort products by rating
    function sortProductsByRating(products) {
        products.sort((a, b) => {
            const ratingA = a.querySelectorAll('.fa-star, .fa-star-half-alt').length;
            const ratingB = b.querySelectorAll('.fa-star, .fa-star-half-alt').length;
            
            return ratingB - ratingA;
        });
    }
    
    // Sort products by newest (using data-id as a proxy for product age)
    function sortProductsByNewest(products) {
        products.sort((a, b) => {
            const idA = parseInt(a.querySelector('.add-to-cart').getAttribute('data-id'));
            const idB = parseInt(b.querySelector('.add-to-cart').getAttribute('data-id'));
            
            return idB - idA; // Assuming higher IDs are newer products
        });
    }
    
    // Handle pagination
    const paginationButtons = document.querySelectorAll('.pagination button');
    
    paginationButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            paginationButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // In a real application, you would load the next page of products here
            // For this demo, we'll just scroll to the top of the products section
            document.querySelector('.shop-content').scrollIntoView({ behavior: 'smooth' });
        });
    });
});