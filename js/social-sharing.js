// Social Sharing Functionality

class SocialSharing {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.createShareButtons();
    }

    bindEvents() {
        // Listen for share button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.share-btn')) {
                e.preventDefault();
                const shareBtn = e.target.closest('.share-btn');
                const platform = shareBtn.dataset.platform;
                const url = shareBtn.dataset.url || window.location.href;
                const title = shareBtn.dataset.title || document.title;
                const description = shareBtn.dataset.description || '';
                const image = shareBtn.dataset.image || '';
                
                this.share(platform, { url, title, description, image });
            }
        });

        // Listen for product share events
        document.addEventListener('click', (e) => {
            if (e.target.closest('.product-share-btn')) {
                e.preventDefault();
                const productElement = e.target.closest('.product-card, .product-details');
                if (productElement) {
                    this.showShareModal(this.getProductShareData(productElement));
                }
            }
        });
    }

    createShareButtons() {
        // Add share buttons to product cards
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach(card => {
            if (!card.querySelector('.product-share-btn')) {
                this.addShareButtonToProduct(card);
            }
        });

        // Add share buttons to product detail pages
        const productDetails = document.querySelector('.product-details');
        if (productDetails && !productDetails.querySelector('.share-section')) {
            this.addShareSectionToProductDetail(productDetails);
        }
    }

    addShareButtonToProduct(productCard) {
        const actionsDiv = productCard.querySelector('.product-actions');
        if (actionsDiv) {
            const shareBtn = document.createElement('button');
            shareBtn.className = 'product-share-btn';
            shareBtn.innerHTML = '<i class="fas fa-share-alt"></i>';
            shareBtn.title = 'Share Product';
            actionsDiv.appendChild(shareBtn);
        }
    }

    addShareSectionToProductDetail(productDetails) {
        const shareSection = document.createElement('div');
        shareSection.className = 'share-section';
        shareSection.innerHTML = `
            <h3>Share this product</h3>
            <div class="share-buttons">
                <button class="share-btn whatsapp" data-platform="whatsapp">
                    <i class="fab fa-whatsapp"></i> WhatsApp
                </button>
                <button class="share-btn facebook" data-platform="facebook">
                    <i class="fab fa-facebook"></i> Facebook
                </button>
                <button class="share-btn twitter" data-platform="twitter">
                    <i class="fab fa-twitter"></i> Twitter
                </button>
                <button class="share-btn instagram" data-platform="instagram">
                    <i class="fab fa-instagram"></i> Instagram
                </button>
                <button class="share-btn copy-link" data-platform="copy">
                    <i class="fas fa-link"></i> Copy Link
                </button>
            </div>
        `;

        // Insert after product info
        const productInfo = productDetails.querySelector('.product-info');
        if (productInfo) {
            productInfo.appendChild(shareSection);
        } else {
            productDetails.appendChild(shareSection);
        }

        // Set share data
        const shareData = this.getProductShareData(productDetails);
        const shareButtons = shareSection.querySelectorAll('.share-btn');
        shareButtons.forEach(btn => {
            btn.dataset.url = shareData.url;
            btn.dataset.title = shareData.title;
            btn.dataset.description = shareData.description;
            btn.dataset.image = shareData.image;
        });
    }

    getProductShareData(productElement) {
        const productName = productElement.querySelector('.product-name, h1')?.textContent?.trim() || 'Amazing Product';
        const productPrice = productElement.querySelector('.product-price, .price')?.textContent?.trim() || '';
        const productImage = productElement.querySelector('.product-image img, .product-thumbnail img')?.src || '';
        const productUrl = window.location.href;
        
        return {
            url: productUrl,
            title: `${productName} - Infinite Shadow`,
            description: `Check out this amazing anime merchandise: ${productName} ${productPrice ? 'for ' + productPrice : ''} at Infinite Shadow!`,
            image: productImage
        };
    }

    share(platform, data) {
        const { url, title, description, image } = data;
        const encodedUrl = encodeURIComponent(url);
        const encodedTitle = encodeURIComponent(title);
        const encodedDescription = encodeURIComponent(description);
        const encodedImage = encodeURIComponent(image);

        let shareUrl = '';
        let windowFeatures = 'width=600,height=400,scrollbars=yes,resizable=yes';

        switch (platform) {
            case 'whatsapp':
                const whatsappText = `${title}\n\n${description}\n\n${url}`;
                shareUrl = `https://wa.me/?text=${encodeURIComponent(whatsappText)}`;
                break;

            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedDescription}`;
                break;

            case 'twitter':
                const twitterText = `${title} ${description}`;
                shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(twitterText)}&url=${encodedUrl}`;
                break;

            case 'instagram':
                // Instagram doesn't support direct URL sharing, so we'll copy the link and show instructions
                this.copyToClipboard(url);
                this.showInstagramInstructions(data);
                return;

            case 'linkedin':
                shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;
                break;

            case 'pinterest':
                shareUrl = `https://pinterest.com/pin/create/button/?url=${encodedUrl}&media=${encodedImage}&description=${encodedDescription}`;
                break;

            case 'telegram':
                shareUrl = `https://t.me/share/url?url=${encodedUrl}&text=${encodedDescription}`;
                break;

            case 'copy':
                this.copyToClipboard(url);
                this.showNotification('Link copied to clipboard!', 'success');
                return;

            case 'email':
                const emailSubject = encodeURIComponent(title);
                const emailBody = encodeURIComponent(`${description}\n\nCheck it out: ${url}`);
                shareUrl = `mailto:?subject=${emailSubject}&body=${emailBody}`;
                window.location.href = shareUrl;
                return;

            default:
                console.error('Unknown sharing platform:', platform);
                return;
        }

        if (shareUrl) {
            window.open(shareUrl, '_blank', windowFeatures);
        }
    }

    showShareModal(shareData) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('shareModal');
        if (!modal) {
            modal = this.createShareModal();
            document.body.appendChild(modal);
        }

        // Update modal content
        const modalTitle = modal.querySelector('.modal-title');
        const shareButtons = modal.querySelectorAll('.share-btn');
        
        modalTitle.textContent = 'Share ' + shareData.title;
        
        shareButtons.forEach(btn => {
            btn.dataset.url = shareData.url;
            btn.dataset.title = shareData.title;
            btn.dataset.description = shareData.description;
            btn.dataset.image = shareData.image;
        });

        // Show modal
        modal.style.display = 'block';
    }

    createShareModal() {
        const modal = document.createElement('div');
        modal.id = 'shareModal';
        modal.className = 'modal share-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">Share Product</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="share-grid">
                        <button class="share-btn whatsapp" data-platform="whatsapp">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp</span>
                        </button>
                        <button class="share-btn facebook" data-platform="facebook">
                            <i class="fab fa-facebook"></i>
                            <span>Facebook</span>
                        </button>
                        <button class="share-btn twitter" data-platform="twitter">
                            <i class="fab fa-twitter"></i>
                            <span>Twitter</span>
                        </button>
                        <button class="share-btn instagram" data-platform="instagram">
                            <i class="fab fa-instagram"></i>
                            <span>Instagram</span>
                        </button>
                        <button class="share-btn linkedin" data-platform="linkedin">
                            <i class="fab fa-linkedin"></i>
                            <span>LinkedIn</span>
                        </button>
                        <button class="share-btn telegram" data-platform="telegram">
                            <i class="fab fa-telegram"></i>
                            <span>Telegram</span>
                        </button>
                        <button class="share-btn copy-link" data-platform="copy">
                            <i class="fas fa-link"></i>
                            <span>Copy Link</span>
                        </button>
                        <button class="share-btn email" data-platform="email">
                            <i class="fas fa-envelope"></i>
                            <span>Email</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add close functionality
        const closeBtn = modal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });

        // Close when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        return modal;
    }

    showInstagramInstructions(data) {
        const instructions = `
            Link copied to clipboard!
            
            To share on Instagram:
            1. Open Instagram app
            2. Create a new post or story
            3. Add your content
            4. Paste the link in your caption or bio
            
            Product: ${data.title}
            Link: ${data.url}
        `;
        
        this.showNotification(instructions, 'info', 8000);
    }

    copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            // Use modern clipboard API
            navigator.clipboard.writeText(text).then(() => {
                console.log('Text copied to clipboard');
            }).catch(err => {
                console.error('Failed to copy text: ', err);
                this.fallbackCopyToClipboard(text);
            });
        } else {
            // Fallback for older browsers
            this.fallbackCopyToClipboard(text);
        }
    }

    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            console.log('Text copied to clipboard (fallback)');
        } catch (err) {
            console.error('Failed to copy text (fallback): ', err);
        }
        
        document.body.removeChild(textArea);
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message.replace(/\n/g, '<br>')}</span>
            </div>
            <button class="notification-close">&times;</button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Position notification
        const notifications = document.querySelectorAll('.notification');
        const offset = (notifications.length - 1) * 70;
        notification.style.top = `${20 + offset}px`;

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto remove
        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);

        // Manual close
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });
    }

    removeNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // Method to add share functionality to any element
    addShareButton(element, shareData) {
        const shareBtn = document.createElement('button');
        shareBtn.className = 'share-trigger-btn';
        shareBtn.innerHTML = '<i class="fas fa-share-alt"></i> Share';
        shareBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.showShareModal(shareData);
        });
        
        element.appendChild(shareBtn);
    }

    // Method to generate share URLs programmatically
    generateShareUrl(platform, data) {
        const { url, title, description, image } = data;
        const encodedUrl = encodeURIComponent(url);
        const encodedTitle = encodeURIComponent(title);
        const encodedDescription = encodeURIComponent(description);

        switch (platform) {
            case 'whatsapp':
                const whatsappText = `${title}\n\n${description}\n\n${url}`;
                return `https://wa.me/?text=${encodeURIComponent(whatsappText)}`;
            case 'facebook':
                return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedDescription}`;
            case 'twitter':
                const twitterText = `${title} ${description}`;
                return `https://twitter.com/intent/tweet?text=${encodeURIComponent(twitterText)}&url=${encodedUrl}`;
            case 'linkedin':
                return `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;
            case 'telegram':
                return `https://t.me/share/url?url=${encodedUrl}&text=${encodedDescription}`;
            default:
                return url;
        }
    }
}

// Initialize social sharing when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.socialSharing = new SocialSharing();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SocialSharing;
}
