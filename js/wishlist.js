// Wishlist Management JavaScript

class WishlistManager {
    constructor() {
        this.wishlist = [];
        this.isLoading = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadWishlist();
        this.updateWishlistIcon();
    }

    bindEvents() {
        // Share wishlist
        const shareBtn = document.getElementById('shareWishlistBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.openShareModal());
        }

        // Clear wishlist
        const clearBtn = document.getElementById('clearWishlistBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearWishlist());
        }

        // Share modal events
        this.bindShareModalEvents();

        // Global wishlist events
        document.addEventListener('wishlistUpdated', () => {
            this.loadWishlist();
            this.updateWishlistIcon();
        });
    }

    bindShareModalEvents() {
        const shareModal = document.getElementById('shareModal');
        const closeBtn = document.getElementById('closeShareModal');
        const cancelBtn = document.getElementById('cancelShareBtn');
        const generateBtn = document.getElementById('generateShareBtn');
        const copyBtn = document.getElementById('copyUrlBtn');
        const makePublicCheckbox = document.getElementById('makePublic');

        if (closeBtn) closeBtn.addEventListener('click', () => this.closeShareModal());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.closeShareModal());
        if (generateBtn) generateBtn.addEventListener('click', () => this.generateShareLink());
        if (copyBtn) copyBtn.addEventListener('click', () => this.copyShareUrl());

        if (makePublicCheckbox) {
            makePublicCheckbox.addEventListener('change', (e) => {
                const urlGroup = document.getElementById('shareUrlGroup');
                if (urlGroup) {
                    urlGroup.style.display = e.target.checked ? 'block' : 'none';
                }
            });
        }

        // Close modal when clicking outside
        if (shareModal) {
            shareModal.addEventListener('click', (e) => {
                if (e.target === shareModal) {
                    this.closeShareModal();
                }
            });
        }
    }

    async loadWishlist() {
        this.showLoading();

        try {
            const response = await fetch('api/wishlist.php?action=list');
            const data = await response.json();

            if (data.success) {
                this.wishlist = data.data;
                this.displayWishlist();
                this.loadWishlistStats();
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            console.error('Error loading wishlist:', error);
            this.showError('Failed to load wishlist');
        } finally {
            this.hideLoading();
        }
    }

    async loadWishlistStats() {
        try {
            const response = await fetch('api/wishlist.php?action=stats');
            const data = await response.json();

            if (data.success) {
                this.displayStats(data.data);
            }
        } catch (error) {
            console.error('Error loading wishlist stats:', error);
        }
    }

    displayWishlist() {
        const grid = document.getElementById('wishlistGrid');
        const emptyState = document.getElementById('emptyWishlist');
        const actions = document.getElementById('wishlistActions');
        const stats = document.getElementById('wishlistStats');

        if (this.wishlist.length === 0) {
            grid.style.display = 'none';
            actions.style.display = 'none';
            stats.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        actions.style.display = 'flex';
        stats.style.display = 'block';
        grid.style.display = 'grid';

        grid.innerHTML = '';

        this.wishlist.forEach(item => {
            const itemElement = this.createWishlistItem(item);
            grid.appendChild(itemElement);
        });
    }

    createWishlistItem(item) {
        const div = document.createElement('div');
        div.className = 'wishlist-item';
        div.dataset.productId = item.product_id;

        const price = item.sale_price || item.price;
        const originalPrice = item.sale_price ? item.price : null;

        div.innerHTML = `
            <div class="wishlist-item-image">
                <img src="${item.primary_image || 'images/placeholder.jpg'}" alt="${item.name}">
                <div class="wishlist-item-actions">
                    <button class="wishlist-action-btn" onclick="wishlistManager.removeFromWishlist(${item.product_id})" title="Remove from wishlist">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="wishlist-action-btn" onclick="wishlistManager.shareProduct(${item.product_id})" title="Share product">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
            </div>
            <div class="wishlist-item-info">
                <h3 class="wishlist-item-title">${item.name}</h3>
                <div class="wishlist-item-price">
                    $${parseFloat(price).toFixed(2)}
                    ${originalPrice ? `<span class="original-price">$${parseFloat(originalPrice).toFixed(2)}</span>` : ''}
                </div>
                <div class="wishlist-item-buttons">
                    <button class="btn" onclick="wishlistManager.moveToCart(${item.product_id})">
                        <i class="fas fa-shopping-cart"></i> Add to Cart
                    </button>
                    <button class="btn btn-secondary" onclick="window.location.href='product-detail.html?id=${item.product_id}'">
                        View Details
                    </button>
                </div>
            </div>
        `;

        return div;
    }

    displayStats(stats) {
        document.getElementById('totalItems').textContent = stats.total_items;
        document.getElementById('totalValue').textContent = `$${stats.total_value.toFixed(2)}`;
        document.getElementById('avgPrice').textContent = `$${stats.average_price.toFixed(2)}`;
    }

    async addToWishlist(productId) {
        try {
            const response = await fetch('api/wishlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'add',
                    product_id: productId
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Added to wishlist!', 'success');
                this.updateWishlistIcon(data.wishlist_count);
                
                // Dispatch event for other components
                document.dispatchEvent(new CustomEvent('wishlistUpdated', {
                    detail: { action: 'add', productId, count: data.wishlist_count }
                }));
            } else {
                this.showNotification(data.message, 'error');
            }

            return data;
        } catch (error) {
            console.error('Error adding to wishlist:', error);
            this.showNotification('Error adding to wishlist', 'error');
            return { success: false };
        }
    }

    async removeFromWishlist(productId) {
        if (!confirm('Remove this item from your wishlist?')) {
            return;
        }

        try {
            const response = await fetch(`api/wishlist.php?product_id=${productId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Removed from wishlist', 'success');
                this.updateWishlistIcon(data.wishlist_count);
                
                // Remove item from display
                const itemElement = document.querySelector(`[data-product-id="${productId}"]`);
                if (itemElement) {
                    itemElement.remove();
                }

                // Update local wishlist array
                this.wishlist = this.wishlist.filter(item => item.product_id != productId);
                
                // Refresh display if empty
                if (this.wishlist.length === 0) {
                    this.displayWishlist();
                } else {
                    this.loadWishlistStats();
                }

                // Dispatch event
                document.dispatchEvent(new CustomEvent('wishlistUpdated', {
                    detail: { action: 'remove', productId, count: data.wishlist_count }
                }));
            } else {
                this.showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error removing from wishlist:', error);
            this.showNotification('Error removing from wishlist', 'error');
        }
    }

    async moveToCart(productId) {
        try {
            const response = await fetch('api/wishlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'move_to_cart',
                    product_id: productId,
                    quantity: 1
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Moved to cart!', 'success');
                
                // Remove from wishlist display
                const itemElement = document.querySelector(`[data-product-id="${productId}"]`);
                if (itemElement) {
                    itemElement.remove();
                }

                // Update local wishlist array
                this.wishlist = this.wishlist.filter(item => item.product_id != productId);
                
                // Refresh display if empty
                if (this.wishlist.length === 0) {
                    this.displayWishlist();
                } else {
                    this.loadWishlistStats();
                }

                // Update cart icon
                this.updateCartIcon();
                
                // Dispatch events
                document.dispatchEvent(new CustomEvent('wishlistUpdated'));
                document.dispatchEvent(new CustomEvent('cartUpdated'));
            } else {
                this.showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error moving to cart:', error);
            this.showNotification('Error moving to cart', 'error');
        }
    }

    async clearWishlist() {
        if (!confirm('Are you sure you want to clear your entire wishlist?')) {
            return;
        }

        try {
            const response = await fetch('api/wishlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'clear'
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Wishlist cleared', 'success');
                this.wishlist = [];
                this.displayWishlist();
                this.updateWishlistIcon(0);
                
                // Dispatch event
                document.dispatchEvent(new CustomEvent('wishlistUpdated', {
                    detail: { action: 'clear', count: 0 }
                }));
            } else {
                this.showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error clearing wishlist:', error);
            this.showNotification('Error clearing wishlist', 'error');
        }
    }

    async shareProduct(productId) {
        const product = this.wishlist.find(item => item.product_id == productId);
        if (!product) return;

        const shareData = {
            title: product.name,
            text: `Check out this amazing ${product.name} from Infinite Shadow!`,
            url: `${window.location.origin}/product-detail.html?id=${productId}`
        };

        if (window.socialSharing) {
            window.socialSharing.shareProduct(shareData);
        } else {
            // Fallback
            if (navigator.share) {
                try {
                    await navigator.share(shareData);
                } catch (error) {
                    console.log('Share cancelled');
                }
            } else {
                // Copy to clipboard
                navigator.clipboard.writeText(shareData.url);
                this.showNotification('Product link copied to clipboard!', 'success');
            }
        }
    }

    openShareModal() {
        const modal = document.getElementById('shareModal');
        if (modal) {
            modal.style.display = 'block';
        }
    }

    closeShareModal() {
        const modal = document.getElementById('shareModal');
        if (modal) {
            modal.style.display = 'none';
        }
        
        // Reset form
        document.getElementById('makePublic').checked = false;
        document.getElementById('shareUrlGroup').style.display = 'none';
        document.getElementById('shareUrl').value = '';
    }

    async generateShareLink() {
        const isPublic = document.getElementById('makePublic').checked;

        try {
            const response = await fetch('api/wishlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'share',
                    is_public: isPublic
                })
            });

            const data = await response.json();

            if (data.success) {
                const shareUrl = `${window.location.origin}/wishlist-shared.html?token=${data.data.share_token}`;
                document.getElementById('shareUrl').value = shareUrl;
                document.getElementById('shareUrlGroup').style.display = 'block';
                this.showNotification('Share link generated!', 'success');
            } else {
                this.showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('Error generating share link:', error);
            this.showNotification('Error generating share link', 'error');
        }
    }

    copyShareUrl() {
        const shareUrl = document.getElementById('shareUrl');
        shareUrl.select();
        document.execCommand('copy');
        this.showNotification('Share link copied to clipboard!', 'success');
    }

    async updateWishlistIcon(count = null) {
        if (count === null) {
            try {
                const response = await fetch('api/wishlist.php?action=count');
                const data = await response.json();
                count = data.success ? data.count : 0;
            } catch (error) {
                count = 0;
            }
        }

        // Update wishlist icon if it exists
        const wishlistCount = document.querySelector('.wishlist-count');
        if (wishlistCount) {
            wishlistCount.textContent = count;
            wishlistCount.style.display = count > 0 ? 'flex' : 'none';
        }
    }

    async updateCartIcon() {
        try {
            const response = await fetch('api/cart.php?action=count');
            const data = await response.json();
            const count = data.success ? data.count : 0;

            const cartCount = document.querySelector('.cart-count');
            if (cartCount) {
                cartCount.textContent = count;
            }
        } catch (error) {
            console.error('Error updating cart icon:', error);
        }
    }

    showLoading() {
        document.getElementById('wishlistLoading').style.display = 'block';
        document.getElementById('wishlistGrid').style.display = 'none';
        document.getElementById('emptyWishlist').style.display = 'none';
        document.getElementById('wishlistActions').style.display = 'none';
        document.getElementById('wishlistStats').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('wishlistLoading').style.display = 'none';
    }

    showNotification(message, type = 'info') {
        if (window.socialSharing) {
            window.socialSharing.showNotification(message, type);
        } else {
            // Fallback
            alert(message);
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    // Public method to check if product is in wishlist
    async isInWishlist(productId) {
        try {
            const response = await fetch(`api/wishlist.php?action=check&product_id=${productId}`);
            const data = await response.json();
            return data.success ? data.in_wishlist : false;
        } catch (error) {
            return false;
        }
    }
}

// Initialize wishlist manager
document.addEventListener('DOMContentLoaded', () => {
    window.wishlistManager = new WishlistManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WishlistManager;
}
