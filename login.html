<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login/Signup - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Auth Page Styles */
        .auth-page {
            padding: 60px 0;
            min-height: 80vh;
            display: flex;
            align-items: center;
        }

        .auth-container {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            box-shadow: 0 10px 30px var(--shadow-color);
            border-radius: 15px;
            overflow: hidden;
        }

        .auth-image {
            flex: 1;
            background: linear-gradient(135deg, rgba(106, 61, 232, 0.8), rgba(255, 107, 107, 0.8)), url('images/1.jpg');
            background-size: cover;
            background-position: center;
            padding: 40px;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .auth-image h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .auth-image p {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .auth-image .features {
            list-style: none;
            padding: 0;
        }

        .auth-image .features li {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            font-size: 1.05rem;
        }

        .auth-image .features i {
            margin-right: 10px;
            color: var(--accent-color);
            font-size: 1.2rem;
        }

        .auth-forms {
            flex: 1;
            background-color: var(--card-bg);
            padding: 40px;
        }

        .auth-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid var(--border-color);
        }

        .auth-tab {
            flex: 1;
            text-align: center;
            padding: 15px;
            cursor: pointer;
            font-weight: 600;
            position: relative;
            transition: all 0.3s ease;
        }

        .auth-tab.active {
            color: var(--primary-color);
        }

        .auth-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary-color);
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.2);
            outline: none;
        }

        .form-group input.error {
            border-color: var(--secondary-color);
        }

        .form-group .password-field {
            position: relative;
        }

        .form-group .toggle-password {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .form-group .toggle-password:hover {
            opacity: 1;
            color: var(--primary-color);
        }

        .form-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .remember-me {
            display: flex;
            align-items: center;
        }

        .remember-me input {
            margin-right: 8px;
            cursor: pointer;
        }

        .remember-me label {
            cursor: pointer;
        }

        .forgot-password {
            color: var(--primary-color);
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .auth-btn {
            width: 100%;
            padding: 15px;
            font-size: 1.1rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, var(--primary-color), #8a5cf5);
            border: none;
            cursor: pointer;
        }

        .auth-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .social-login {
            text-align: center;
        }

        .social-login p {
            margin-bottom: 15px;
            position: relative;
        }

        .social-login p::before,
        .social-login p::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 40%;
            height: 1px;
            background-color: var(--border-color);
        }

        .social-login p::before {
            left: 0;
        }

        .social-login p::after {
            right: 0;
        }

        .social-icons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--bg-color);
            color: var(--text-color);
            font-size: 1.2rem;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px var(--shadow-color);
        }

        .social-icons a:hover {
            transform: translateY(-3px);
        }

        .social-icons .facebook:hover {
            background-color: #3b5998;
            color: white;
        }

        .social-icons .google:hover {
            background-color: #db4437;
            color: white;
        }

        .social-icons .twitter:hover {
            background-color: #1da1f2;
            color: white;
        }

        .error-message {
            color: var(--secondary-color);
            font-size: 0.8rem;
            margin-top: 5px;
            display: none;
        }

        .success-message {
            background-color: #48c774;
            color: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin-top: 20px;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .auth-container {
                max-width: 90%;
            }
        }

        @media (max-width: 768px) {
            .auth-container {
                flex-direction: column;
                max-width: 95%;
            }

            .auth-image {
                padding: 30px;
                min-height: 250px;
            }

            .auth-image h2 {
                font-size: 2rem;
            }

            .auth-forms {
                padding: 30px 20px;
            }

            .form-footer {
                flex-direction: column;
                align-items: flex-start;
            }

            .forgot-password {
                margin-top: 10px;
            }
        }

        @media (max-width: 480px) {
            .auth-image {
                padding: 25px 15px;
            }

            .auth-image h2 {
                font-size: 1.8rem;
            }

            .auth-image .features li {
                font-size: 0.95rem;
            }

            .auth-forms {
                padding: 25px 15px;
            }

            .auth-tab {
                padding: 12px 5px;
                font-size: 0.9rem;
            }

            .social-icons a {
                width: 45px;
                height: 45px;
            }
        }
    </style>
</head>

<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="cart.html">Cart</a></li>
                    <li><a href="login.html" class="active">Login</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="cart-icon">
                    <a href="cart.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">0</span>
                    </a>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <section class="auth-page">
            <div class="container">
                <div class="auth-container">
                    <div class="auth-image">
                        <h2>Welcome to Infinite Shadow</h2>
                        <p>Join our community of anime enthusiasts and enjoy exclusive benefits:</p>
                        <ul class="features">
                            <li><i class="fas fa-check-circle"></i> Exclusive member discounts</li>
                            <li><i class="fas fa-check-circle"></i> Early access to new products</li>
                            <li><i class="fas fa-check-circle"></i> Special limited edition items</li>
                            <li><i class="fas fa-check-circle"></i> Free shipping on orders over $50</li>
                            <li><i class="fas fa-check-circle"></i> Personalized recommendations</li>
                        </ul>
                    </div>
                    
                    <div class="auth-forms">
                        <div class="auth-tabs">
                            <div class="auth-tab active" data-tab="login">Login</div>
                            <div class="auth-tab" data-tab="signup">Sign Up</div>
                        </div>
                        
                        <!-- Login Form -->
                        <form class="auth-form active" id="login-form">
                            <div class="form-group">
                                <label for="login-email">Email Address</label>
                                <input type="email" id="login-email" required>
                                <small class="error-message"></small>
                            </div>
                            
                            <div class="form-group">
                                <label for="login-password">Password</label>
                                <div class="password-field">
                                    <input type="password" id="login-password" required>
                                    <i class="fas fa-eye toggle-password"></i>
                                </div>
                                <small class="error-message"></small>
                            </div>
                            
                            <div class="form-footer">
                                <div class="remember-me">
                                    <input type="checkbox" id="remember-me">
                                    <label for="remember-me">Remember me</label>
                                </div>
                                <a href="#" class="forgot-password">Forgot Password?</a>
                            </div>
                            
                            <button type="submit" class="btn primary-btn auth-btn">Login</button>
                            
                            <div class="social-login">
                                <p>Or login with</p>
                                <div class="social-icons">
                                    <a href="#" class="facebook"><i class="fab fa-facebook-f"></i></a>
                                    <a href="#" class="google"><i class="fab fa-google"></i></a>
                                    <a href="#" class="twitter"><i class="fab fa-twitter"></i></a>
                                </div>
                            </div>
                        </form>
                        
                        <!-- Signup Form -->
                        <form class="auth-form" id="signup-form">
                            <div class="form-group">
                                <label for="signup-name">Full Name</label>
                                <input type="text" id="signup-name" required>
                                <small class="error-message"></small>
                            </div>
                            
                            <div class="form-group">
                                <label for="signup-email">Email Address</label>
                                <input type="email" id="signup-email" required>
                                <small class="error-message"></small>
                            </div>
                            
                            <div class="form-group">
                                <label for="signup-password">Password</label>
                                <div class="password-field">
                                    <input type="password" id="signup-password" required>
                                    <i class="fas fa-eye toggle-password"></i>
                                </div>
                                <small class="error-message"></small>
                            </div>
                            
                            <div class="form-group">
                                <label for="signup-confirm-password">Confirm Password</label>
                                <div class="password-field">
                                    <input type="password" id="signup-confirm-password" required>
                                    <i class="fas fa-eye toggle-password"></i>
                                </div>
                                <small class="error-message"></small>
                            </div>
                            
                            <div class="form-footer">
                                <div class="remember-me">
                                    <input type="checkbox" id="agree-terms" required>
                                    <label for="agree-terms">I agree to the <a href="#">Terms & Conditions</a></label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn primary-btn auth-btn">Create Account</button>
                            
                            <div class="social-login">
                                <p>Or sign up with</p>
                                <div class="social-icons">
                                    <a href="#" class="facebook"><i class="fab fa-facebook-f"></i></a>
                                    <a href="#" class="google"><i class="fab fa-google"></i></a>
                                    <a href="#" class="twitter"><i class="fab fa-twitter"></i></a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate destination for premium anime merchandise.</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=posters">Posters</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h3>Newsletter</h3>
                    <p>Subscribe to get special offers and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
                <div class="social-icons">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-discord"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/login.js"></script>
</body>

</html>