<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Test - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Performance optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Critical CSS inlined for faster loading -->
    <style>
        /* Critical above-the-fold styles */
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        
        header {
            background-color: #2a2a2a;
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .hero {
            padding: 120px 0 80px;
            text-align: center;
            background: linear-gradient(135deg, #6a3de8, #ff6b6b);
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #6a3de8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="search-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </div>
                <div class="user-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <h1>Performance Optimization Test</h1>
                <p>Testing responsive design, lazy loading, and performance optimizations</p>
                <button class="btn" onclick="runPerformanceTests()">Run Performance Tests</button>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <h2>Performance Metrics</h2>
                <div class="performance-grid">
                    <div class="metric-card">
                        <h3>Page Load Time</h3>
                        <div class="metric-value" id="loadTime">
                            <div class="loading-spinner"></div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <h3>First Contentful Paint</h3>
                        <div class="metric-value" id="fcp">
                            <div class="loading-spinner"></div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <h3>Largest Contentful Paint</h3>
                        <div class="metric-value" id="lcp">
                            <div class="loading-spinner"></div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <h3>Cumulative Layout Shift</h3>
                        <div class="metric-value" id="cls">
                            <div class="loading-spinner"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <h2>Responsive Design Test</h2>
                <div class="responsive-test">
                    <div class="breakpoint-info">
                        <p>Current Breakpoint: <span id="currentBreakpoint">Loading...</span></p>
                        <p>Screen Width: <span id="screenWidth">Loading...</span>px</p>
                        <p>Device Type: <span id="deviceType">Loading...</span></p>
                    </div>
                    
                    <div class="responsive-grid">
                        <div class="responsive-item">Mobile (≤480px)</div>
                        <div class="responsive-item">Tablet (≤768px)</div>
                        <div class="responsive-item">Desktop (≤1024px)</div>
                        <div class="responsive-item">Large (>1024px)</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <h2>Lazy Loading Test</h2>
                <div class="lazy-loading-test">
                    <p>Scroll down to test lazy loading of images:</p>
                    
                    <!-- Placeholder images for lazy loading test -->
                    <div class="image-grid">
                        <img data-src="https://picsum.photos/300/200?random=1" alt="Lazy loaded image 1" class="lazy-image">
                        <img data-src="https://picsum.photos/300/200?random=2" alt="Lazy loaded image 2" class="lazy-image">
                        <img data-src="https://picsum.photos/300/200?random=3" alt="Lazy loaded image 3" class="lazy-image">
                        <img data-src="https://picsum.photos/300/200?random=4" alt="Lazy loaded image 4" class="lazy-image">
                        <img data-src="https://picsum.photos/300/200?random=5" alt="Lazy loaded image 5" class="lazy-image">
                        <img data-src="https://picsum.photos/300/200?random=6" alt="Lazy loaded image 6" class="lazy-image">
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <h2>Touch Interaction Test</h2>
                <div class="touch-test">
                    <div class="touch-area" id="touchArea">
                        <p>Touch/Click this area to test interactions</p>
                        <div class="touch-feedback" id="touchFeedback"></div>
                    </div>
                    
                    <div class="swipe-area" id="swipeArea">
                        <p>Swipe left or right in this area</p>
                        <div class="swipe-indicator" id="swipeIndicator">No swipe detected</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Performance</h3>
                    <ul>
                        <li><a href="#responsive">Responsive Design</a></li>
                        <li><a href="#optimization">Optimization</a></li>
                        <li><a href="#testing">Testing</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Features</h3>
                    <ul>
                        <li><a href="#lazy-loading">Lazy Loading</a></li>
                        <li><a href="#touch">Touch Support</a></li>
                        <li><a href="#mobile">Mobile First</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. Performance optimized.</p>
            </div>
        </div>
    </footer>

    <!-- Performance monitoring script -->
    <script>
        // Performance monitoring
        function measurePerformance() {
            if ('performance' in window) {
                const navigation = performance.getEntriesByType('navigation')[0];
                const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
                
                document.getElementById('loadTime').textContent = `${loadTime.toFixed(2)}ms`;
                
                // Web Vitals if available
                if ('PerformanceObserver' in window) {
                    // First Contentful Paint
                    new PerformanceObserver((list) => {
                        for (const entry of list.getEntries()) {
                            if (entry.name === 'first-contentful-paint') {
                                document.getElementById('fcp').textContent = `${entry.startTime.toFixed(2)}ms`;
                            }
                        }
                    }).observe({ entryTypes: ['paint'] });
                    
                    // Largest Contentful Paint
                    new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        const lastEntry = entries[entries.length - 1];
                        document.getElementById('lcp').textContent = `${lastEntry.startTime.toFixed(2)}ms`;
                    }).observe({ entryTypes: ['largest-contentful-paint'] });
                    
                    // Cumulative Layout Shift
                    new PerformanceObserver((list) => {
                        let clsValue = 0;
                        for (const entry of list.getEntries()) {
                            if (!entry.hadRecentInput) {
                                clsValue += entry.value;
                            }
                        }
                        document.getElementById('cls').textContent = clsValue.toFixed(4);
                    }).observe({ entryTypes: ['layout-shift'] });
                }
            }
        }
        
        // Run performance tests
        function runPerformanceTests() {
            measurePerformance();
            
            // Test responsive breakpoints
            updateResponsiveInfo();
            
            // Test lazy loading
            testLazyLoading();
        }
        
        function updateResponsiveInfo() {
            const width = window.innerWidth;
            let breakpoint, deviceType;
            
            if (width <= 480) {
                breakpoint = 'Mobile';
                deviceType = 'Phone';
            } else if (width <= 768) {
                breakpoint = 'Tablet';
                deviceType = 'Tablet';
            } else if (width <= 1024) {
                breakpoint = 'Desktop';
                deviceType = 'Desktop';
            } else {
                breakpoint = 'Large';
                deviceType = 'Large Desktop';
            }
            
            document.getElementById('currentBreakpoint').textContent = breakpoint;
            document.getElementById('screenWidth').textContent = width;
            document.getElementById('deviceType').textContent = deviceType;
        }
        
        function testLazyLoading() {
            const lazyImages = document.querySelectorAll('.lazy-image');
            
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            lazyImages.forEach(img => imageObserver.observe(img));
        }
        
        // Touch interaction tests
        function initTouchTests() {
            const touchArea = document.getElementById('touchArea');
            const touchFeedback = document.getElementById('touchFeedback');
            const swipeArea = document.getElementById('swipeArea');
            const swipeIndicator = document.getElementById('swipeIndicator');
            
            let touchCount = 0;
            let touchStartX = 0;
            
            touchArea.addEventListener('touchstart', (e) => {
                touchCount++;
                touchFeedback.textContent = `Touch ${touchCount}: Started`;
            });
            
            touchArea.addEventListener('touchend', (e) => {
                touchFeedback.textContent = `Touch ${touchCount}: Ended`;
            });
            
            touchArea.addEventListener('click', (e) => {
                touchCount++;
                touchFeedback.textContent = `Click ${touchCount}: Detected`;
            });
            
            // Swipe detection
            swipeArea.addEventListener('touchstart', (e) => {
                touchStartX = e.touches[0].clientX;
            });
            
            swipeArea.addEventListener('touchend', (e) => {
                const touchEndX = e.changedTouches[0].clientX;
                const deltaX = touchEndX - touchStartX;
                
                if (Math.abs(deltaX) > 50) {
                    const direction = deltaX > 0 ? 'Right' : 'Left';
                    swipeIndicator.textContent = `Swiped ${direction} (${Math.abs(deltaX).toFixed(0)}px)`;
                }
            });
        }
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            measurePerformance();
            updateResponsiveInfo();
            testLazyLoading();
            initTouchTests();
            
            // Update responsive info on resize
            window.addEventListener('resize', updateResponsiveInfo);
        });
    </script>
    
    <!-- Load non-critical scripts asynchronously -->
    <script src="js/responsive.js" async></script>
    <script src="js/social-sharing.js" async></script>
    <script src="js/chatbot.js" async></script>
</body>
</html>
