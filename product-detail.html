<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Detail - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .product-detail-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .product-detail {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .product-images {
            position: relative;
        }
        
        .main-image {
            width: 100%;
            height: 500px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .image-thumbnails {
            display: flex;
            gap: 10px;
            overflow-x: auto;
        }
        
        .thumbnail {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        
        .thumbnail.active,
        .thumbnail:hover {
            opacity: 1;
        }
        
        .product-info h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .product-rating {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .stars {
            color: var(--accent-color);
        }
        
        .price-section {
            margin-bottom: 30px;
        }
        
        .current-price {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .original-price {
            font-size: 1.2rem;
            text-decoration: line-through;
            color: var(--text-color);
            opacity: 0.6;
            margin-left: 10px;
        }
        
        .discount-badge {
            background-color: var(--secondary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 10px;
        }
        
        .product-description {
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .product-options {
            margin-bottom: 30px;
        }
        
        .option-group {
            margin-bottom: 20px;
        }
        
        .option-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .quantity-btn {
            width: 40px;
            height: 40px;
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .quantity-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .quantity-input {
            width: 60px;
            text-align: center;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--card-bg);
            color: var(--text-color);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .add-to-cart-btn {
            flex: 1;
            padding: 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .add-to-cart-btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
        }
        
        .wishlist-btn {
            padding: 15px;
            background-color: transparent;
            color: var(--text-color);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .wishlist-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .product-meta {
            border-top: 1px solid var(--border-color);
            padding-top: 20px;
            margin-top: 20px;
        }
        
        .meta-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .related-products {
            margin-top: 60px;
        }
        
        .related-products h2 {
            text-align: center;
            margin-bottom: 40px;
            color: var(--primary-color);
        }
        
        @media (max-width: 768px) {
            .product-detail {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .product-info h1 {
                font-size: 1.5rem;
            }
            
            .current-price {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html" class="active">Shop</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="search-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </div>
                <div class="user-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="product-detail-container">
            <div class="product-detail">
                <div class="product-images">
                    <img src="images/product1.jpg" alt="Product Image" class="main-image" id="mainImage">
                    <div class="image-thumbnails" id="thumbnails">
                        <img src="images/product1.jpg" alt="Thumbnail 1" class="thumbnail active" onclick="changeMainImage(this)">
                        <img src="images/product2.jpg" alt="Thumbnail 2" class="thumbnail" onclick="changeMainImage(this)">
                        <img src="images/product3.jpg" alt="Thumbnail 3" class="thumbnail" onclick="changeMainImage(this)">
                    </div>
                </div>
                
                <div class="product-info">
                    <h1 id="productName">Anime Figure Collection</h1>
                    
                    <div class="product-rating">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                        </div>
                        <span>(4.5) 128 reviews</span>
                    </div>
                    
                    <div class="price-section">
                        <span class="current-price" id="currentPrice">$89.99</span>
                        <span class="original-price" id="originalPrice">$119.99</span>
                        <span class="discount-badge">25% OFF</span>
                    </div>
                    
                    <div class="product-description">
                        <p>Experience the ultimate anime collection with this premium figure set. Crafted with exceptional attention to detail, each piece captures the essence of your favorite characters. Perfect for collectors and anime enthusiasts alike.</p>
                    </div>
                    
                    <div class="product-options">
                        <div class="option-group">
                            <label for="size">Size:</label>
                            <select id="size" name="size">
                                <option value="small">Small (6 inches)</option>
                                <option value="medium" selected>Medium (8 inches)</option>
                                <option value="large">Large (10 inches)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="quantity-selector">
                        <label>Quantity:</label>
                        <button class="quantity-btn" onclick="decreaseQuantity()">-</button>
                        <input type="number" id="quantity" class="quantity-input" value="1" min="1" max="10">
                        <button class="quantity-btn" onclick="increaseQuantity()">+</button>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="add-to-cart-btn" onclick="addToCart()">
                            <i class="fas fa-shopping-cart"></i> Add to Cart
                        </button>
                        <button class="wishlist-btn" onclick="addToWishlist()">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                    
                    <!-- Share Section will be added here by social-sharing.js -->
                    
                    <div class="product-meta">
                        <div class="meta-item">
                            <span>SKU:</span>
                            <span>ANM-001</span>
                        </div>
                        <div class="meta-item">
                            <span>Category:</span>
                            <span>Figures</span>
                        </div>
                        <div class="meta-item">
                            <span>Tags:</span>
                            <span>Anime, Collectible, Figure</span>
                        </div>
                        <div class="meta-item">
                            <span>Availability:</span>
                            <span class="in-stock">In Stock</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="related-products">
                <h2>Related Products</h2>
                <div class="products-grid" id="relatedProducts">
                    <!-- Related products will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="#">Figures</a></li>
                        <li><a href="#">Posters</a></li>
                        <li><a href="#">Accessories</a></li>
                        <li><a href="#">Clothing</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Customer Service</h3>
                    <ul>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Shipping</a></li>
                        <li><a href="#">Returns</a></li>
                        <li><a href="#">Support</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Follow Us</h3>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/product-detail.js"></script>
    <script src="js/social-sharing.js"></script>
</body>
</html>
