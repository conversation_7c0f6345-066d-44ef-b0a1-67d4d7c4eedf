<?php
session_start();

// Check if setup is already completed
if (file_exists('config/config.php')) {
    $config = include 'config/config.php';
    if (isset($config['setup_completed']) && $config['setup_completed']) {
        header('Location: index.html');
        exit;
    }
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// Create necessary directories
$directories = ['config', 'uploads', 'uploads/products', 'uploads/files', 'uploads/temp'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1: // Database Configuration
            $db_host = trim($_POST['db_host']);
            $db_name = trim($_POST['db_name']);
            $db_user = trim($_POST['db_user']);
            $db_pass = trim($_POST['db_pass']);
            
            // Test database connection with multiple methods
            $connectionMethods = [
                // Method 1: Standard TCP connection
                "mysql:host=$db_host;charset=utf8mb4",
                // Method 2: Unix socket (common on local installations)
                "mysql:unix_socket=/tmp/mysql.sock;charset=utf8mb4",
                // Method 3: Alternative socket locations
                "mysql:unix_socket=/var/run/mysqld/mysqld.sock;charset=utf8mb4",
                // Method 4: MAMP/XAMPP socket
                "mysql:unix_socket=/Applications/MAMP/tmp/mysql/mysql.sock;charset=utf8mb4",
                // Method 5: Localhost with explicit IP
                "mysql:host=127.0.0.1;charset=utf8mb4",
                // Method 6: Standard without charset (fallback)
                "mysql:host=$db_host"
            ];

            $pdo = null;
            $connectionError = "";

            foreach ($connectionMethods as $dsn) {
                try {
                    $pdo = new PDO($dsn, $db_user, $db_pass, [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_TIMEOUT => 5
                    ]);

                    // Test the connection
                    $pdo->query("SELECT 1");
                    break; // Connection successful

                } catch (PDOException $e) {
                    $connectionError = $e->getMessage();
                    $pdo = null;
                    continue;
                }
            }

            if ($pdo === null) {
                throw new PDOException("All connection methods failed. Last error: " . $connectionError .
                    "\n\nPlease check:\n" .
                    "1. MySQL/MariaDB is running\n" .
                    "2. Database credentials are correct\n" .
                    "3. Database server is accessible\n" .
                    "4. PHP PDO MySQL extension is installed");
            }

            try {
                // Create database if it doesn't exist
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$db_name`");
                
                // Execute database schema
                $sql = file_get_contents('database.sql');
                $statements = explode(';', $sql);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                // Save database config
                $config = [
                    'db_host' => $db_host,
                    'db_name' => $db_name,
                    'db_user' => $db_user,
                    'db_pass' => $db_pass,
                    'setup_step' => 2
                ];
                
                file_put_contents('config/config.php', '<?php return ' . var_export($config, true) . ';');
                
                header('Location: setup.php?step=2');
                exit;
                
            } catch (PDOException $e) {
                $error = 'Database connection failed: ' . $e->getMessage();
            }
            break;
            
        case 2: // Admin User Configuration
            $admin_username = trim($_POST['admin_username']);
            $admin_email = trim($_POST['admin_email']);
            $admin_password = trim($_POST['admin_password']);
            $admin_confirm = trim($_POST['admin_confirm']);
            $admin_first_name = trim($_POST['admin_first_name']);
            $admin_last_name = trim($_POST['admin_last_name']);
            
            if ($admin_password !== $admin_confirm) {
                $error = 'Passwords do not match';
            } elseif (strlen($admin_password) < 6) {
                $error = 'Password must be at least 6 characters long';
            } else {
                try {
                    $config = include 'config/config.php';

                    // Try multiple connection methods
                    $connectionMethods = [
                        "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4",
                        "mysql:unix_socket=/tmp/mysql.sock;dbname={$config['db_name']};charset=utf8mb4",
                        "mysql:unix_socket=/var/run/mysqld/mysqld.sock;dbname={$config['db_name']};charset=utf8mb4",
                        "mysql:unix_socket=/Applications/MAMP/tmp/mysql/mysql.sock;dbname={$config['db_name']};charset=utf8mb4",
                        "mysql:host=127.0.0.1;dbname={$config['db_name']};charset=utf8mb4"
                    ];

                    $pdo = null;
                    foreach ($connectionMethods as $dsn) {
                        try {
                            $pdo = new PDO($dsn, $config['db_user'], $config['db_pass'], [
                                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                            ]);
                            break;
                        } catch (PDOException $e) {
                            continue;
                        }
                    }

                    if ($pdo === null) {
                        throw new PDOException("Could not connect to database");
                    }
                    
                    // Check if admin user already exists
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE role = 'admin' LIMIT 1");
                    $stmt->execute();
                    
                    if ($stmt->rowCount() > 0) {
                        $error = 'Admin user already exists';
                    } else {
                        // Create admin user
                        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) VALUES (?, ?, ?, ?, ?, 'admin', 'active', 1)");
                        $stmt->execute([$admin_username, $admin_email, $hashed_password, $admin_first_name, $admin_last_name]);
                        
                        // Update config
                        $config['setup_step'] = 3;
                        file_put_contents('config/config.php', '<?php return ' . var_export($config, true) . ';');
                        
                        header('Location: setup.php?step=3');
                        exit;
                    }
                } catch (PDOException $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
            }
            break;
            
        case 3: // OpenRouter API Configuration
            $api_key = trim($_POST['openrouter_api_key']);
            $enable_chatbot = isset($_POST['enable_chatbot']) ? 1 : 0;
            
            try {
                $config = include 'config/config.php';

                // Try multiple connection methods
                $connectionMethods = [
                    "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4",
                    "mysql:unix_socket=/tmp/mysql.sock;dbname={$config['db_name']};charset=utf8mb4",
                    "mysql:unix_socket=/var/run/mysqld/mysqld.sock;dbname={$config['db_name']};charset=utf8mb4",
                    "mysql:unix_socket=/Applications/MAMP/tmp/mysql/mysql.sock;dbname={$config['db_name']};charset=utf8mb4",
                    "mysql:host=127.0.0.1;dbname={$config['db_name']};charset=utf8mb4"
                ];

                $pdo = null;
                foreach ($connectionMethods as $dsn) {
                    try {
                        $pdo = new PDO($dsn, $config['db_user'], $config['db_pass'], [
                            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                        ]);
                        break;
                    } catch (PDOException $e) {
                        continue;
                    }
                }

                if ($pdo === null) {
                    throw new PDOException("Could not connect to database");
                }
                
                // Update chatbot settings
                $status = $enable_chatbot && !empty($api_key) ? 'active' : 'inactive';
                $stmt = $pdo->prepare("UPDATE chatbot_settings SET openrouter_api_key = ?, status = ? WHERE id = 1");
                $stmt->execute([$api_key, $status]);
                
                // Complete setup
                $config['openrouter_api_key'] = $api_key;
                $config['chatbot_enabled'] = $enable_chatbot;
                $config['setup_completed'] = true;
                unset($config['setup_step']);
                
                file_put_contents('config/config.php', '<?php return ' . var_export($config, true) . ';');
                
                header('Location: setup.php?step=4');
                exit;
                
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
            break;
    }
}

// Load existing config if available
$config = [];
if (file_exists('config/config.php')) {
    $config = include 'config/config.php';
    if (isset($config['setup_step']) && $config['setup_step'] > $step) {
        $step = $config['setup_step'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .setup-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 40px;
            background-color: var(--card-bg);
            border-radius: 15px;
            box-shadow: 0 10px 30px var(--shadow-color);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .setup-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .setup-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: 600;
            position: relative;
        }
        
        .step.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .step.completed {
            background-color: var(--accent-color);
            color: #333;
        }
        
        .step.inactive {
            background-color: var(--border-color);
            color: var(--text-color);
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background-color: var(--border-color);
            transform: translateY(-50%);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .error {
            background-color: rgba(255, 107, 107, 0.1);
            color: var(--secondary-color);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }
        
        .success {
            background-color: rgba(72, 199, 116, 0.1);
            color: #48c774;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid rgba(72, 199, 116, 0.3);
        }
        
        .setup-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        
        .btn-secondary {
            background-color: var(--border-color);
            color: var(--text-color);
        }
        
        .btn-secondary:hover {
            background-color: var(--text-color);
            color: var(--bg-color);
        }
        
        .completion-message {
            text-align: center;
            padding: 40px 20px;
        }
        
        .completion-message i {
            font-size: 4rem;
            color: var(--accent-color);
            margin-bottom: 20px;
        }
        
        .completion-message h2 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
    </style>
</head>
<body class="dark-mode">
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <h1>Infinite<span style="color: var(--primary-color);">Shadow</span> Setup</h1>
                <p>Let's get your anime store ready!</p>
            </div>
            
            <div class="setup-steps">
                <div class="step <?= $step > 1 ? 'completed' : ($step == 1 ? 'active' : 'inactive') ?>">1</div>
                <div class="step <?= $step > 2 ? 'completed' : ($step == 2 ? 'active' : 'inactive') ?>">2</div>
                <div class="step <?= $step > 3 ? 'completed' : ($step == 3 ? 'active' : 'inactive') ?>">3</div>
                <div class="step <?= $step == 4 ? 'active' : 'inactive' ?>">4</div>
            </div>
            
            <?php if ($error): ?>
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="success">
                    <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <!-- Step 1: Database Configuration -->
                <h2>Database Configuration</h2>
                <p>Please enter your MySQL database connection details:</p>

                <div class="info" style="background-color: rgba(23, 162, 184, 0.1); color: #0c5460; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(23, 162, 184, 0.3);">
                    <strong>💡 Connection Issues?</strong><br>
                    If you're having trouble connecting, try these tools:<br>
                    • <a href="db-config-helper.php" target="_blank" style="color: #0c5460; text-decoration: underline;">🔧 Auto-detect database settings</a><br>
                    • <a href="test-db-connection.php" target="_blank" style="color: #0c5460; text-decoration: underline;">🧪 Test database connection</a><br>
                    • <strong>MAMP/XAMPP:</strong> Host: localhost, Port: 8889 (MAMP) or 3306 (XAMPP)<br>
                    • <strong>Local MySQL:</strong> Host: localhost or 127.0.0.1
                </div>

                <form method="POST">
                    <div class="form-group">
                        <label for="db_host">Database Host</label>
                        <input type="text" id="db_host" name="db_host" value="<?= isset($_GET['db_host']) ? htmlspecialchars($_GET['db_host']) : (isset($config['db_host']) ? htmlspecialchars($config['db_host']) : 'localhost') ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="db_name">Database Name</label>
                        <input type="text" id="db_name" name="db_name" value="<?= isset($config['db_name']) ? htmlspecialchars($config['db_name']) : 'infinite_shadow_ecommerce' ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="db_user">Database Username</label>
                        <input type="text" id="db_user" name="db_user" value="<?= isset($_GET['db_user']) ? htmlspecialchars($_GET['db_user']) : (isset($config['db_user']) ? htmlspecialchars($config['db_user']) : 'root') ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="db_pass">Database Password</label>
                        <input type="password" id="db_pass" name="db_pass" value="<?= isset($_GET['db_pass']) ? htmlspecialchars($_GET['db_pass']) : '' ?>">
                    </div>

                    <div class="setup-actions">
                        <div></div>
                        <button type="submit" class="btn">Next Step <i class="fas fa-arrow-right"></i></button>
                    </div>
                </form>

            <?php elseif ($step == 2): ?>
                <!-- Step 2: Admin User Configuration -->
                <h2>Admin User Setup</h2>
                <p>Create your administrator account:</p>

                <form method="POST">
                    <div class="form-group">
                        <label for="admin_username">Username</label>
                        <input type="text" id="admin_username" name="admin_username" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_email">Email Address</label>
                        <input type="email" id="admin_email" name="admin_email" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_first_name">First Name</label>
                        <input type="text" id="admin_first_name" name="admin_first_name" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_last_name">Last Name</label>
                        <input type="text" id="admin_last_name" name="admin_last_name" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_password">Password</label>
                        <input type="password" id="admin_password" name="admin_password" required minlength="6">
                    </div>

                    <div class="form-group">
                        <label for="admin_confirm">Confirm Password</label>
                        <input type="password" id="admin_confirm" name="admin_confirm" required minlength="6">
                    </div>

                    <div class="setup-actions">
                        <a href="setup.php?step=1" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Previous</a>
                        <button type="submit" class="btn">Next Step <i class="fas fa-arrow-right"></i></button>
                    </div>
                </form>

            <?php elseif ($step == 3): ?>
                <!-- Step 3: OpenRouter API Configuration -->
                <h2>Chatbot Configuration</h2>
                <p>Configure the AI chatbot using OpenRouter API (optional):</p>

                <form method="POST">
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="enable_chatbot" name="enable_chatbot" <?= isset($config['chatbot_enabled']) && $config['chatbot_enabled'] ? 'checked' : '' ?>>
                            <label for="enable_chatbot">Enable AI Chatbot</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="openrouter_api_key">OpenRouter API Key</label>
                        <input type="text" id="openrouter_api_key" name="openrouter_api_key" value="<?= isset($config['openrouter_api_key']) ? htmlspecialchars($config['openrouter_api_key']) : '' ?>" placeholder="sk-or-...">
                        <small style="color: var(--text-color); opacity: 0.7; font-size: 0.9rem;">
                            Get your free API key from <a href="https://openrouter.ai" target="_blank" style="color: var(--primary-color);">OpenRouter.ai</a>
                        </small>
                    </div>

                    <div class="setup-actions">
                        <a href="setup.php?step=2" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Previous</a>
                        <button type="submit" class="btn">Complete Setup <i class="fas fa-check"></i></button>
                    </div>
                </form>

            <?php elseif ($step == 4): ?>
                <!-- Step 4: Setup Complete -->
                <div class="completion-message">
                    <i class="fas fa-check-circle"></i>
                    <h2>Setup Complete!</h2>
                    <p>Your Infinite Shadow e-commerce store is now ready to use.</p>

                    <div style="margin: 30px 0;">
                        <a href="index.html" class="btn" style="margin-right: 10px;">
                            <i class="fas fa-home"></i> Visit Store
                        </a>
                        <a href="admin/admin.html" class="btn btn-secondary">
                            <i class="fas fa-cog"></i> Admin Panel
                        </a>
                    </div>

                    <div style="background-color: var(--bg-color); padding: 20px; border-radius: 8px; margin-top: 20px; text-align: left;">
                        <h3 style="margin-bottom: 15px; color: var(--primary-color);">Next Steps:</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 10px;"><i class="fas fa-check" style="color: var(--accent-color); margin-right: 10px;"></i> Add products to your store</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check" style="color: var(--accent-color); margin-right: 10px;"></i> Configure payment methods</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check" style="color: var(--accent-color); margin-right: 10px;"></i> Set up shipping options</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check" style="color: var(--accent-color); margin-right: 10px;"></i> Customize your store settings</li>
                            <li><i class="fas fa-check" style="color: var(--accent-color); margin-right: 10px;"></i> Test the chatbot functionality</li>
                        </ul>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Toggle chatbot API key field based on checkbox
        document.addEventListener('DOMContentLoaded', function() {
            const enableChatbot = document.getElementById('enable_chatbot');
            const apiKeyField = document.getElementById('openrouter_api_key');

            if (enableChatbot && apiKeyField) {
                function toggleApiKey() {
                    apiKeyField.disabled = !enableChatbot.checked;
                    apiKeyField.required = enableChatbot.checked;
                }

                enableChatbot.addEventListener('change', toggleApiKey);
                toggleApiKey(); // Initial state
            }
        });
    </script>
</body>
</html>
