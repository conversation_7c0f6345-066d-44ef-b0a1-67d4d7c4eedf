<?php
/**
 * Database Connection Test Script
 * Use this to diagnose database connection issues
 */

// Default connection parameters
$defaultParams = [
    'host' => 'localhost',
    'user' => 'root',
    'pass' => '',
    'port' => 3306
];

// Get parameters from URL or use defaults
$host = $_GET['host'] ?? $defaultParams['host'];
$user = $_GET['user'] ?? $defaultParams['user'];
$pass = $_GET['pass'] ?? $defaultParams['pass'];
$port = $_GET['port'] ?? $defaultParams['port'];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Connection Test</h1>
        <p>This tool helps diagnose MySQL/MariaDB connection issues.</p>

        <form method="GET">
            <div class="form-group">
                <label for="host">Database Host:</label>
                <input type="text" id="host" name="host" value="<?= htmlspecialchars($host) ?>">
            </div>
            
            <div class="form-group">
                <label for="user">Database User:</label>
                <input type="text" id="user" name="user" value="<?= htmlspecialchars($user) ?>">
            </div>
            
            <div class="form-group">
                <label for="pass">Database Password:</label>
                <input type="password" id="pass" name="pass" value="<?= htmlspecialchars($pass) ?>">
            </div>
            
            <div class="form-group">
                <label for="port">Database Port:</label>
                <input type="text" id="port" name="port" value="<?= htmlspecialchars($port) ?>">
            </div>
            
            <button type="submit">Test Connection</button>
        </form>

        <hr style="margin: 30px 0;">

        <h2>Connection Test Results</h2>

        <?php
        // Check if PDO MySQL extension is loaded
        if (!extension_loaded('pdo_mysql')) {
            echo '<div class="test-result error">❌ PDO MySQL extension is not loaded. Please install php-mysql package.</div>';
        } else {
            echo '<div class="test-result success">✅ PDO MySQL extension is loaded.</div>';
        }

        // Test different connection methods
        $connectionMethods = [
            [
                'name' => 'Standard TCP Connection',
                'dsn' => "mysql:host=$host;port=$port;charset=utf8mb4"
            ],
            [
                'name' => 'TCP with 127.0.0.1',
                'dsn' => "mysql:host=127.0.0.1;port=$port;charset=utf8mb4"
            ],
            [
                'name' => 'Unix Socket (/tmp/mysql.sock)',
                'dsn' => "mysql:unix_socket=/tmp/mysql.sock;charset=utf8mb4"
            ],
            [
                'name' => 'Unix Socket (/var/run/mysqld/mysqld.sock)',
                'dsn' => "mysql:unix_socket=/var/run/mysqld/mysqld.sock;charset=utf8mb4"
            ],
            [
                'name' => 'MAMP Socket',
                'dsn' => "mysql:unix_socket=/Applications/MAMP/tmp/mysql/mysql.sock;charset=utf8mb4"
            ],
            [
                'name' => 'XAMPP Socket',
                'dsn' => "mysql:unix_socket=/opt/lampp/var/mysql/mysql.sock;charset=utf8mb4"
            ]
        ];

        $successfulConnection = null;

        foreach ($connectionMethods as $method) {
            echo "<h3>{$method['name']}</h3>";
            echo "<div class='code'>DSN: {$method['dsn']}</div>";
            
            try {
                $pdo = new PDO($method['dsn'], $user, $pass, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_TIMEOUT => 5
                ]);
                
                // Test the connection
                $result = $pdo->query("SELECT VERSION() as version, NOW() as current_time");
                $info = $result->fetch(PDO::FETCH_ASSOC);
                
                echo '<div class="test-result success">';
                echo "✅ Connection successful!<br>";
                echo "MySQL Version: {$info['version']}<br>";
                echo "Current Time: {$info['current_time']}";
                echo '</div>';
                
                if ($successfulConnection === null) {
                    $successfulConnection = $method['dsn'];
                }
                
            } catch (PDOException $e) {
                echo '<div class="test-result error">';
                echo "❌ Connection failed: " . $e->getMessage();
                echo '</div>';
            }
        }

        // Show system information
        echo '<hr style="margin: 30px 0;">';
        echo '<h2>System Information</h2>';
        
        echo '<div class="test-result info">';
        echo '<strong>PHP Version:</strong> ' . PHP_VERSION . '<br>';
        echo '<strong>Operating System:</strong> ' . PHP_OS . '<br>';
        echo '<strong>Server Software:</strong> ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . '<br>';
        echo '<strong>PDO Drivers:</strong> ' . implode(', ', PDO::getAvailableDrivers()) . '<br>';
        echo '</div>';

        // Check for common MySQL processes
        echo '<h3>MySQL Process Check</h3>';
        
        if (function_exists('shell_exec')) {
            $processes = shell_exec('ps aux | grep mysql | grep -v grep');
            if ($processes) {
                echo '<div class="test-result success">✅ MySQL processes found:</div>';
                echo '<div class="code">' . htmlspecialchars($processes) . '</div>';
            } else {
                echo '<div class="test-result warning">⚠️ No MySQL processes found. MySQL might not be running.</div>';
            }
        } else {
            echo '<div class="test-result warning">⚠️ Cannot check MySQL processes (shell_exec disabled).</div>';
        }

        // Show socket file locations
        echo '<h3>Socket File Check</h3>';
        $socketPaths = [
            '/tmp/mysql.sock',
            '/var/run/mysqld/mysqld.sock',
            '/Applications/MAMP/tmp/mysql/mysql.sock',
            '/opt/lampp/var/mysql/mysql.sock'
        ];

        foreach ($socketPaths as $socket) {
            if (file_exists($socket)) {
                echo "<div class='test-result success'>✅ Socket found: $socket</div>";
            } else {
                echo "<div class='test-result info'>ℹ️ Socket not found: $socket</div>";
            }
        }

        // Recommendations
        echo '<hr style="margin: 30px 0;">';
        echo '<h2>Recommendations</h2>';

        if ($successfulConnection) {
            echo '<div class="test-result success">';
            echo "✅ <strong>Success!</strong> Use this DSN in your setup:<br>";
            echo "<div class='code'>$successfulConnection</div>";
            echo '</div>';
        } else {
            echo '<div class="test-result error">';
            echo "❌ <strong>No successful connections.</strong> Try these steps:<br>";
            echo "<ol>";
            echo "<li>Make sure MySQL/MariaDB is installed and running</li>";
            echo "<li>Check your database credentials</li>";
            echo "<li>Try connecting with a MySQL client (mysql command line, phpMyAdmin, etc.)</li>";
            echo "<li>Check if the database server is listening on the expected port</li>";
            echo "<li>Verify firewall settings</li>";
            echo "</ol>";
            echo '</div>';
        }

        // Common solutions
        echo '<div class="test-result info">';
        echo "<strong>Common Solutions:</strong><br>";
        echo "<ul>";
        echo "<li><strong>MAMP/XAMPP:</strong> Use the socket path or check the control panel for the correct port</li>";
        echo "<li><strong>Homebrew MySQL:</strong> Try socket path /tmp/mysql.sock</li>";
        echo "<li><strong>System MySQL:</strong> Try socket path /var/run/mysqld/mysqld.sock</li>";
        echo "<li><strong>Docker:</strong> Make sure the container is running and ports are mapped correctly</li>";
        echo "<li><strong>Remote MySQL:</strong> Check firewall and ensure remote connections are allowed</li>";
        echo "</ul>";
        echo '</div>';
        ?>
    </div>
</body>
</html>
