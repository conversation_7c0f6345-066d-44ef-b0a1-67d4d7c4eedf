<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Generator - Infinite Shadow</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .invoice-page {
            padding: 60px 0;
        }
        
        .invoice-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .invoice-form,
        .invoice-preview {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .invoice-form h2,
        .invoice-preview h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: span 2;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .items-table th {
            font-weight: 600;
            background-color: rgba(106, 61, 232, 0.1);
        }
        
        .items-table input {
            width: 100%;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .add-item-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .add-item-btn:hover {
            background-color: var(--secondary-color);
        }
        
        .remove-item-btn {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .remove-item-btn:hover {
            opacity: 0.8;
        }
        
        .summary-section {
            margin-top: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
        }
        
        .summary-left {
            padding-right: 20px;
        }
        
        .summary-right {
            border-left: 1px solid var(--border-color);
            padding-left: 20px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .summary-total {
            font-weight: 700;
            font-size: 1.2rem;
            border-top: 2px solid var(--border-color);
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            gap: 15px;
        }
        
        .action-buttons button {
            flex: 1;
        }
        
        .btn-secondary {
            background-color: var(--bg-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            background-color: var(--border-color);
        }
        
        /* Invoice Preview Styles */
        .invoice-preview {
            margin-top: 40px;
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .company-info h2 {
            border-bottom: none;
            padding-bottom: 0;
            margin-bottom: 5px;
        }
        
        .company-info p {
            margin: 0;
            line-height: 1.6;
        }
        
        .invoice-details {
            text-align: right;
        }
        
        .invoice-details h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .invoice-details p {
            margin: 5px 0;
        }
        
        .customer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .customer-info,
        .shipping-info {
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 15px;
        }
        
        .customer-info h3,
        .shipping-info h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .preview-items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .preview-items-table th,
        .preview-items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .preview-items-table th {
            font-weight: 600;
            background-color: rgba(106, 61, 232, 0.1);
        }
        
        .preview-summary {
            width: 350px;
            margin-left: auto;
        }
        
        .preview-summary .summary-item {
            padding: 8px 0;
        }
        
        .invoice-footer {
            margin-top: 50px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            color: var(--text-color);
            opacity: 0.8;
        }
        
        .invoice-footer p {
            margin: 5px 0;
        }
        
        .print-section {
            display: none;
        }
        
        @media print {
            header, .invoice-form, .action-buttons {
                display: none;
            }
            
            .print-section {
                display: block;
            }
            
            .invoice-preview {
                box-shadow: none;
                padding: 0;
                margin: 0;
            }
            
            body {
                background-color: white;
                color: black;
            }
        }
        
        @media (max-width: 768px) {
            .form-grid,
            .customer-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .form-group.full-width {
                grid-column: span 1;
            }
            
            .summary-section {
                grid-template-columns: 1fr;
            }
            
            .summary-right {
                border-left: none;
                padding-left: 0;
                margin-top: 20px;
                padding-top: 20px;
                border-top: 1px solid var(--border-color);
            }
            
            .invoice-header {
                flex-direction: column;
                gap: 20px;
            }
            
            .invoice-details {
                text-align: left;
            }
            
            .preview-summary {
                width: 100%;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="../index.html">Home</a></li>
                    <li><a href="../shop.html">Shop</a></li>
                    <li><a href="../admin/admin.html">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <section class="invoice-page">
            <div class="container">
                <div class="invoice-container">
                    <!-- Invoice Form -->
                    <div class="invoice-form">
                        <h2>Invoice Generator</h2>
                        <form id="invoice-form">
                            <div class="form-section">
                                <h3>Company Information</h3>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="company-name">Company Name</label>
                                        <input type="text" id="company-name" value="Infinite Shadow" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="company-email">Email</label>
                                        <input type="email" id="company-email" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label for="company-phone">Phone</label>
                                        <input type="text" id="company-phone" value="+****************">
                                    </div>
                                    <div class="form-group">
                                        <label for="company-address">Address</label>
                                        <input type="text" id="company-address" value="123 Anime Street, Tokyo, Japan">
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Customer Information</h3>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="customer-name">Customer Name</label>
                                        <input type="text" id="customer-name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="customer-email">Email</label>
                                        <input type="email" id="customer-email">
                                    </div>
                                    <div class="form-group">
                                        <label for="customer-phone">Phone</label>
                                        <input type="text" id="customer-phone">
                                    </div>
                                    <div class="form-group">
                                        <label for="customer-address">Billing Address</label>
                                        <input type="text" id="customer-address">
                                    </div>
                                    <div class="form-group">
                                        <label for="shipping-address">Shipping Address</label>
                                        <input type="text" id="shipping-address">
                                    </div>
                                    <div class="form-group">
                                        <label for="invoice-date">Invoice Date</label>
                                        <input type="date" id="invoice-date" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Invoice Items</h3>
                                <table class="items-table" id="items-table">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Description</th>
                                            <th>Quantity</th>
                                            <th>Price</th>
                                            <th>Total</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="item-rows">
                                        <tr>
                                            <td><input type="text" class="item-name" required></td>
                                            <td><input type="text" class="item-description"></td>
                                            <td><input type="number" class="item-quantity" value="1" min="1" required></td>
                                            <td><input type="number" class="item-price" value="0.00" step="0.01" min="0" required></td>
                                            <td class="item-total">$0.00</td>
                                            <td><button type="button" class="remove-item-btn">Remove</button></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <button type="button" class="add-item-btn" id="add-item-btn">
                                    <i class="fas fa-plus"></i> Add Item
                                </button>

                                <div class="summary-section">
                                    <div class="summary-left">
                                        <div class="form-group">
                                            <label for="notes">Notes</label>
                                            <textarea id="notes" placeholder="Additional notes or payment instructions"></textarea>
                                        </div>
                                    </div>
                                    <div class="summary-right">
                                        <div class="summary-item">
                                            <span>Subtotal</span>
                                            <span id="subtotal">$0.00</span>
                                        </div>
                                        <div class="form-group">
                                            <label for="tax-rate">Tax Rate (%)</label>
                                            <input type="number" id="tax-rate" value="10" min="0" max="100" step="0.01">
                                        </div>
                                        <div class="summary-item">
                                            <span>Tax</span>
                                            <span id="tax-amount">$0.00</span>
                                        </div>
                                        <div class="form-group">
                                            <label for="shipping-cost">Shipping</label>
                                            <input type="number" id="shipping-cost" value="0.00" min="0" step="0.01">
                                        </div>
                                        <div class="summary-item summary-total">
                                            <span>Total</span>
                                            <span id="total-amount">$0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="action-buttons">
                                <button type="button" class="btn btn-secondary" id="reset-btn">Reset</button>
                                <button type="button" class="btn" id="generate-btn">Generate Invoice</button>
                                <button type="button" class="btn" id="print-btn">Print Invoice</button>
                            </div>
                        </form>
                    </div>

                    <!-- Invoice Preview -->
                    <div class="invoice-preview" id="invoice-preview">
                        <h2>Invoice Preview</h2>
                        <div class="invoice-header">
                            <div class="company-info">
                                <h2 id="preview-company-name">Infinite Shadow</h2>
                                <p id="preview-company-address">123 Anime Street, Tokyo, Japan</p>
                                <p id="preview-company-contact"><EMAIL> | +****************</p>
                            </div>
                            <div class="invoice-details">
                                <h3>INVOICE</h3>
                                <p><strong>Invoice #:</strong> <span id="preview-invoice-number">INV-001</span></p>
                                <p><strong>Date:</strong> <span id="preview-invoice-date">01/01/2023</span></p>
                            </div>
                        </div>

                        <div class="customer-grid">
                            <div class="customer-info">
                                <h3>Bill To</h3>
                                <p id="preview-customer-name">Customer Name</p>
                                <p id="preview-customer-address">Customer Address</p>
                                <p id="preview-customer-contact"><EMAIL> | Phone</p>
                            </div>
                            <div class="shipping-info">
                                <h3>Ship To</h3>
                                <p id="preview-shipping-address">Shipping Address</p>
                            </div>
                        </div>

                        <table class="preview-items-table">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody id="preview-item-rows">
                                <!-- Items will be added here dynamically -->
                            </tbody>
                        </table>

                        <div class="preview-summary">
                            <div class="summary-item">
                                <span>Subtotal</span>
                                <span id="preview-subtotal">$0.00</span>
                            </div>
                            <div class="summary-item">
                                <span>Tax (<span id="preview-tax-rate">10</span>%)</span>
                                <span id="preview-tax-amount">$0.00</span>
                            </div>
                            <div class="summary-item">
                                <span>Shipping</span>
                                <span id="preview-shipping">$0.00</span>
                            </div>
                            <div class="summary-item summary-total">
                                <span>Total</span>
                                <span id="preview-total">$0.00</span>
                            </div>
                        </div>

                        <div class="notes-section">
                            <h3>Notes</h3>
                            <p id="preview-notes">Thank you for your business!</p>
                        </div>

                        <div class="invoice-footer">
                            <p>Thank you for shopping with Infinite Shadow!</p>
                            <p>For any questions regarding this invoice, <NAME_EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../shop.html">Shop</a></li>
                        <li><a href="../cart.html">Cart</a></li>
                        <li><a href="../login.html">Account</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="../shop.html?category=figures">Figures</a></li>
                        <li><a href="../shop.html?category=clothing">Clothing</a></li>
                        <li><a href="../shop.html?category=accessories">Accessories</a></li>
                        <li><a href="../shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme Toggle Functionality
            const themeToggle = document.querySelector('.theme-toggle');
            const body = document.body;
            const themeIcon = document.querySelector('.theme-toggle i');

            // Check for saved theme preference or use default
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark-mode') {
                body.classList.add('dark-mode');
            } else if (savedTheme === '') {
                body.classList.remove('dark-mode');
            }

            // Update icon based on current theme
            updateThemeIcon();

            // Toggle theme when clicked
            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    if (body.classList.contains('dark-mode')) {
                        body.classList.remove('dark-mode');
                        localStorage.setItem('theme', '');
                    } else {
                        body.classList.add('dark-mode');
                        localStorage.setItem('theme', 'dark-mode');
                    }
                    updateThemeIcon();
                });
            }

            // Update theme icon based on current theme
            function updateThemeIcon() {
                if (themeIcon) {
                    if (body.classList.contains('dark-mode')) {
                        themeIcon.className = 'fas fa-sun';
                    } else {
                        themeIcon.className = 'fas fa-moon';
                    }
                }
            }
            
            // Mobile Menu Toggle
            const mobileMenuBtn = document.querySelector('.mobile-menu');
            const navLinks = document.querySelector('.nav-links');

            if (mobileMenuBtn && navLinks) {
                mobileMenuBtn.addEventListener('click', () => {
                    navLinks.classList.toggle('active');
                });
            }

            // Invoice Generator Functionality
            const invoiceForm = document.getElementById('invoice-form');
            const addItemBtn = document.getElementById('add-item-btn');
            const itemRows = document.getElementById('item-rows');
            const generateBtn = document.getElementById('generate-btn');
            const printBtn = document.getElementById('print-btn');
            const resetBtn = document.getElementById('reset-btn');

            // Set current date as default
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('invoice-date').value = today;

            // Generate random invoice number
            const invoiceNumber = 'INV-' + Math.floor(1000 + Math.random() * 9000);
            document.getElementById('preview-invoice-number').textContent = invoiceNumber;

            // Add new item row
            addItemBtn.addEventListener('click', function() {
                addItemRow();
            });

            // Add initial item row if none exists
            function addItemRow() {
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td><input type="text" class="item-name" required></td>
                    <td><input type="text" class="item-description"></td>
                    <td><input type="number" class="item-quantity" value="1" min="1" required></td>
                    <td><input type="number" class="item-price" value="0.00" step="0.01" min="0" required></td>
                    <td class="item-total">$0.00</td>
                    <td><button type="button" class="remove-item-btn">Remove</button></td>
                `;
                itemRows.appendChild(newRow);

                // Add event listeners to new row
                const quantityInput = newRow.querySelector('.item-quantity');
                const priceInput = newRow.querySelector('.item-price');
                const removeBtn = newRow.querySelector('.remove-item-btn');

                quantityInput.addEventListener('input', updateRowTotal);
                priceInput.addEventListener('input', updateRowTotal);
                removeBtn.addEventListener('click', function() {
                    if (itemRows.children.length > 1) {
                        itemRows.removeChild(newRow);
                        updateTotals();
                    } else {
                        alert('You need at least one item.');
                    }
                });
            }

            // Update row total when quantity or price changes
            function updateRowTotal() {
                const row = this.closest('tr');
                const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                const price = parseFloat(row.querySelector('.item-price').value) || 0;
                const total = quantity * price;
                row.querySelector('.item-total').textContent = '$' + total.toFixed(2);
                updateTotals();
            }

            // Update all totals
            function updateTotals() {
                let subtotal = 0;
                const rows = itemRows.querySelectorAll('tr');
                
                rows.forEach(row => {
                    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                    const price = parseFloat(row.querySelector('.item-price').value) || 0;
                    subtotal += quantity * price;
                });

                const taxRate = parseFloat(document.getElementById('tax-rate').value) || 0;
                const shippingCost = parseFloat(document.getElementById('shipping-cost').value) || 0;
                const taxAmount = subtotal * (taxRate / 100);
                const total = subtotal + taxAmount + shippingCost;

                document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);
                document.getElementById('tax-amount').textContent = '$' + taxAmount.toFixed(2);
                document.getElementById('total-amount').textContent = '$' + total.toFixed(2);
            }

            // Add event listeners to tax and shipping inputs
            document.getElementById('tax-rate').addEventListener('input', updateTotals);
            document.getElementById('shipping-cost').addEventListener('input', updateTotals);

            // Add event listeners to initial row
            const initialQuantityInputs = document.querySelectorAll('.item-quantity');
            const initialPriceInputs = document.querySelectorAll('.item-price');
            const initialRemoveBtns = document.querySelectorAll('.remove-item-btn');

            initialQuantityInputs.forEach(input => {
                input.addEventListener('input', updateRowTotal);
            });

            initialPriceInputs.forEach(input => {
                input.addEventListener('input', updateRowTotal);
            });

            initialRemoveBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (itemRows.children.length > 1) {
                        itemRows.removeChild(this.