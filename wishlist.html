<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Wishlist - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .wishlist-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .wishlist-header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
        }
        
        .wishlist-content {
            padding: 60px 0;
        }
        
        .wishlist-stats {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .wishlist-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .wishlist-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }
        
        .wishlist-item {
            background-color: var(--card-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: transform 0.3s ease;
            position: relative;
        }
        
        .wishlist-item:hover {
            transform: translateY(-5px);
        }
        
        .wishlist-item-image {
            position: relative;
            height: 250px;
            overflow: hidden;
        }
        
        .wishlist-item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .wishlist-item:hover .wishlist-item-image img {
            transform: scale(1.05);
        }
        
        .wishlist-item-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
        
        .wishlist-action-btn {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: none;
            background-color: rgba(255, 255, 255, 0.9);
            color: var(--text-color);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0;
        }
        
        .wishlist-item:hover .wishlist-action-btn {
            opacity: 1;
        }
        
        .wishlist-action-btn:hover {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .wishlist-item-info {
            padding: 20px;
        }
        
        .wishlist-item-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-color);
        }
        
        .wishlist-item-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .wishlist-item-price .original-price {
            font-size: 0.9rem;
            text-decoration: line-through;
            color: var(--text-color);
            opacity: 0.6;
            margin-left: 10px;
        }
        
        .wishlist-item-buttons {
            display: flex;
            gap: 10px;
        }
        
        .wishlist-item-buttons .btn {
            flex: 1;
            padding: 10px;
            font-size: 0.9rem;
        }
        
        .empty-wishlist {
            text-align: center;
            padding: 60px 20px;
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .empty-wishlist i {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .empty-wishlist h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .empty-wishlist p {
            margin-bottom: 25px;
            opacity: 0.8;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .loading i {
            font-size: 2rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .wishlist-header h1 {
                font-size: 2rem;
            }
            
            .wishlist-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .wishlist-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="search-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </div>
                <div class="user-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <section class="wishlist-header">
            <div class="container">
                <h1>My Wishlist</h1>
                <p>Your favorite anime merchandise collection</p>
            </div>
        </section>

        <section class="wishlist-content">
            <div class="container">
                <!-- Wishlist Stats -->
                <div class="wishlist-stats" id="wishlistStats" style="display: none;">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="totalItems">0</div>
                            <div class="stat-label">Items</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="totalValue">$0</div>
                            <div class="stat-label">Total Value</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="avgPrice">$0</div>
                            <div class="stat-label">Average Price</div>
                        </div>
                    </div>
                </div>

                <!-- Wishlist Actions -->
                <div class="wishlist-actions" id="wishlistActions" style="display: none;">
                    <div>
                        <h2>Your Wishlist</h2>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-secondary" id="shareWishlistBtn">
                            <i class="fas fa-share"></i> Share Wishlist
                        </button>
                        <button class="btn btn-secondary" id="clearWishlistBtn">
                            <i class="fas fa-trash"></i> Clear All
                        </button>
                    </div>
                </div>

                <!-- Loading -->
                <div class="loading" id="wishlistLoading">
                    <i class="fas fa-spinner"></i>
                    <p>Loading your wishlist...</p>
                </div>

                <!-- Wishlist Items -->
                <div class="wishlist-grid" id="wishlistGrid" style="display: none;">
                    <!-- Items will be loaded here -->
                </div>

                <!-- Empty Wishlist -->
                <div class="empty-wishlist" id="emptyWishlist" style="display: none;">
                    <i class="fas fa-heart"></i>
                    <h3>Your wishlist is empty</h3>
                    <p>Start adding your favorite anime merchandise to your wishlist!</p>
                    <a href="shop.html" class="btn">Browse Products</a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="#">Figures</a></li>
                        <li><a href="#">Posters</a></li>
                        <li><a href="#">Accessories</a></li>
                        <li><a href="#">Clothing</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Customer Service</h3>
                    <ul>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Shipping</a></li>
                        <li><a href="#">Returns</a></li>
                        <li><a href="#">Support</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Follow Us</h3>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Share Modal -->
    <div id="shareModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Share Your Wishlist</h2>
                <span class="close" id="closeShareModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="makePublic"> Make wishlist public
                    </label>
                    <small>Allow others to view your wishlist with a shared link</small>
                </div>
                <div class="form-group" id="shareUrlGroup" style="display: none;">
                    <label for="shareUrl">Share URL:</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" id="shareUrl" readonly style="flex: 1;">
                        <button class="btn btn-small" id="copyUrlBtn">Copy</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelShareBtn">Cancel</button>
                <button class="btn" id="generateShareBtn">Generate Link</button>
            </div>
        </div>
    </div>

    <script src="js/wishlist.js"></script>
    <script src="js/social-sharing.js"></script>
    <script src="js/chatbot.js"></script>
    <script src="js/responsive.js"></script>
</body>
</html>
